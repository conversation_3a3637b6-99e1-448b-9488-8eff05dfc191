#include "../headers/EnglandBillingMgr.h"

// External declarations from original decompiled code
extern void* g_vRecvData; // Global receive data queue
extern void* m_hThread;   // Thread handle

namespace RFOnline {
namespace Authentication {
namespace Billing {

// RECV_DATA constructor
RECV_DATA::RECV_DATA() {
    bResult = false;
    dwSeq = 0;
    wType = 0;
    pData = nullptr;
}

// RECV_DATA destructor
RECV_DATA::~RECV_DATA() {
    // Cleanup if needed
}

int EnglandBillingMgr::CallRFOnlineAuth(
    CEnglandBillingMgr* billingMgr,
    _param_cash_select* cashParam,
    int index)
{
    if (!billingMgr || !cashParam) {
        return 1; // Failure
    }

    // Initialize debug buffer with pattern
    __int64 debugBuffer[60];
    for (int i = 0; i < 60; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    // Allocate buffer for message formatting
    char* messageBuffer = new char[0x200];
    memset(messageBuffer, 0, 0x200);

    // Format the billing message with account and index
    sprintf_s(messageBuffer, 0x104, "|%d|%s\n", index + 1, cashParam->in_szAcc);
    
    // Calculate message length (excluding newline)
    unsigned int messageLength = strlen(messageBuffer) - 1;

    // Create header buffer
    char* headerBuffer = new char[10];
    memset(headerBuffer, 0, 10);
    sprintf_s(headerBuffer, 10, "60%05d", messageLength);

    // Calculate total length
    size_t headerLength = strlen(headerBuffer);
    int totalLength = strlen(messageBuffer) + headerLength;

    // Create final message buffer
    char* finalMessage = new char[totalLength + 1];
    memset(finalMessage, 0, totalLength + 1);
    sprintf_s(finalMessage, totalLength + 1, "%s%s", headerBuffer, messageBuffer);

    // Cleanup temporary buffers
    delete[] headerBuffer;
    delete[] messageBuffer;

    // Prepare message type
    char messageType[2] = {1, 0}; // Message type 1

    // Get network instance and send
    size_t finalLength = strlen(finalMessage);
    CEngNetworkBillEX* networkBill = CTSingleton<CEngNetworkBillEX>::Instance();
    int sendResult = networkBill->Send(messageType, finalMessage, finalLength);

    // Log the operation
    billingMgr->m_logBill.Write("Cash Query : %s", finalMessage);

    if (sendResult) {
        // Success - create and queue receive data
        RECV_DATA* receiveData = new RECV_DATA();
        receiveData->bResult = false;
        receiveData->dwSeq = index + 1;
        receiveData->wType = 60;
        receiveData->pData = cashParam;

        // Add to global receive queue
        std::deque<RECV_DATA*>* recvQueue = 
            reinterpret_cast<std::deque<RECV_DATA*>*>(&g_vRecvData);
        recvQueue->push_front(receiveData);

        // Cleanup
        delete receiveData;
        delete[] finalMessage;
        return 0; // Success
    }
    else {
        // Failure - resume thread and log error
        ResumeThread(m_hThread);
        billingMgr->m_logBill.Write("Cash Query Fail.");
        delete[] finalMessage;
        return 1; // Failure
    }
}

int EnglandBillingMgr::ProcessCashQuery(
    CEnglandBillingMgr* billingMgr,
    const char* accountName,
    int index)
{
    if (!billingMgr || !accountName) {
        return 1;
    }

    _param_cash_select cashParam;
    cashParam.in_szAcc = const_cast<char*>(accountName);

    return CallRFOnlineAuth(billingMgr, &cashParam, index);
}

int EnglandBillingMgr::FormatBillingMessage(
    const char* accountName,
    int index,
    char* buffer,
    size_t bufferSize)
{
    if (!accountName || !buffer) {
        return 0;
    }

    return sprintf_s(buffer, bufferSize, "|%d|%s\n", index + 1, accountName);
}

int EnglandBillingMgr::CreateBillingHeader(
    unsigned int dataLength,
    char* buffer,
    size_t bufferSize)
{
    if (!buffer) {
        return 0;
    }

    return sprintf_s(buffer, bufferSize, "60%05d", dataLength);
}

bool EnglandBillingMgr::SendBillingRequest(
    const char* messageType,
    const char* messageData,
    size_t dataLength)
{
    if (!messageType || !messageData) {
        return false;
    }

    CEngNetworkBillEX* networkBill = CTSingleton<CEngNetworkBillEX>::Instance();
    return networkBill->Send(messageType, messageData, dataLength) != 0;
}

void EnglandBillingMgr::QueueReceiveData(
    RECV_DATA* receiveData,
    unsigned int sequenceNumber,
    unsigned short messageType,
    void* paramData)
{
    if (!receiveData) {
        return;
    }

    receiveData->bResult = false;
    receiveData->dwSeq = sequenceNumber;
    receiveData->wType = messageType;
    receiveData->pData = paramData;

    // Add to global receive queue
    std::deque<RECV_DATA*>* recvQueue = 
        reinterpret_cast<std::deque<RECV_DATA*>*>(&g_vRecvData);
    recvQueue->push_front(receiveData);
}

void EnglandBillingMgr::LogBillingOperation(
    CEnglandBillingMgr* billingMgr,
    const char* message,
    const char* data)
{
    if (billingMgr && message) {
        if (data) {
            billingMgr->m_logBill.Write(message, data);
        } else {
            billingMgr->m_logBill.Write(message);
        }
    }
}

} // namespace Billing
} // namespace Authentication
} // namespace RFOnline
