/**
 * @file ValidateDLGroupParameters.h
 * @brief Header for DL Group Parameters Validation Function
 * @note Original Function: ?Validate@?$DL_GroupParameters@VInteger@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x140551AC0
 */

#pragma once
#ifndef VALIDATEDLGROUPPARAMETERS_H
#define VALIDATEDLGROUPPARAMETERS_H

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Validates DL (Discrete Logarithm) group parameters for cryptographic operations
 * @param a1 Pointer to DL_GroupParameters instance (adjusted by offset)
 * @param a2 Pointer to RandomNumberGenerator for validation
 * @param a3 Validation level to perform
 * @return char Returns 1 if parameters are valid, 0 if invalid
 * 
 * This function validates discrete logarithm group parameters used in cryptographic
 * operations such as DSA, ElGamal, and other DL-based algorithms. It performs:
 * 1. Basic parameter existence validation
 * 2. Validation level checking and caching
 * 3. Group parameter mathematical validation
 * 4. Prime validation and group order verification
 */
char __fastcall ValidateDLGroupParameters(
    __int64 a1,
    __int64 a2,
    unsigned int a3);

} // namespace Cryptography
} // namespace Authentication
} // namespace RFOnline

#endif // VALIDATEDLGROUPPARAMETERS_H
