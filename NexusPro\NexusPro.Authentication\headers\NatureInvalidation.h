/**
 * @file NatureInvalidation.h
 * @brief RF Online Nature System Invalidation Function Declarations
 * @note Original Function: ?CN_InvalidateNature@@YAXXZ
 * @note Original Address: 0x140504ED0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: CN_InvalidateNatureYAXXZ_140504ED0.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * @namespace NexusPro::Authentication::Graphics
 * @brief Graphics system functions related to authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Graphics {

/**
 * @brief Invalidates the nature rendering system
 *
 * This function invalidates both the sky and sun rendering systems,
 * typically called when graphics settings change or when the rendering
 * context needs to be refreshed. This ensures that nature elements
 * are properly reinitialized with updated parameters.
 * 
 * **Invalidation Process**:
 * 1. **Sky System**: Clears cached sky textures, atmospheric parameters,
 *    cloud data, and lighting calculations
 * 2. **Sun System**: Clears cached sun position, solar lighting,
 *    shadow mapping data, and time-of-day calculations
 * 
 * **When to Call**:
 * - Graphics device reset or context change
 * - Graphics quality settings modification
 * - Time-of-day system updates
 * - Weather system state changes
 * - Rendering pipeline reconfiguration
 * 
 * **Performance Considerations**:
 * - Should be called sparingly as it forces regeneration of expensive data
 * - Best called during loading screens or scene transitions
 * - May cause temporary frame rate drops during regeneration
 * 
 * @note Original Address: 0x140504ED0
 * @note Part of RF Online's graphics invalidation system
 * @note Ensures proper rendering state consistency
 */
void InvalidateNatureSystem();

} // namespace Graphics
} // namespace Authentication
} // namespace NexusPro
