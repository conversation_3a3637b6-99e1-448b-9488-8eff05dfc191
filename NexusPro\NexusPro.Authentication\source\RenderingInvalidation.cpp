/*
 * NexusPro Authentication Module
 * Rendering Invalidation Implementation
 * 
 * Original Functions: Multiple rendering invalidation functions
 * Original Addresses: 0x1405229B0, 0x1405221E0
 * 
 * Purpose: Handles invalidation of rendering objects like sky and sun during authentication processes
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed variable declarations
 * - Cleaned up virtual function calls
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/RenderingInvalidation.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Rendering {

/**
 * @brief Invalidate sky rendering objects
 * @note Original Function: ?InvalidateSky@Sky@@QEAAXXZ
 * @note Original Address: 0x1405229B0
 */
void __fastcall Sky_InvalidateSky(Sky* this)
{
    Sky* v1 = this;
    
    // Check if sky rendering is enabled (version check)
    if (*reinterpret_cast<float*>(&dword_184A797B0) >= 1.0f) {
        // Release sky texture 1
        uint64_t v2 = *reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(this) + 40);  // *((_QWORD *)this + 5)
        if (v2) {
            // Call virtual destructor/release function
            (*reinterpret_cast<void (**)()>(*reinterpret_cast<uint64_t*>(v2) + 16))();
            *reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v1) + 40) = 0;  // *((_QWORD *)v1 + 5) = 0
        }
        
        // Release sky texture 2
        uint64_t v3 = *reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v1) + 48);  // *((_QWORD *)v1 + 6)
        if (v3) {
            // Call virtual destructor/release function
            (*reinterpret_cast<void (**)()>(*reinterpret_cast<uint64_t*>(v3) + 16))();
            *reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v1) + 48) = 0;  // *((_QWORD *)v1 + 6) = 0
        }
    }
}

/**
 * @brief Invalidate sun rendering objects
 * @note Original Function: ?InvalidateSun@Sun@@QEAAXXZ
 * @note Original Address: 0x1405221E0
 */
void __fastcall Sun_InvalidateSun(Sun* this)
{
    Sun* v1 = this;
    
    // Release sun texture/object
    uint64_t v2 = *reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(this) + 40);  // *((_QWORD *)this + 5)
    if (v2) {
        // Call virtual destructor/release function
        (*reinterpret_cast<void (**)()>(*reinterpret_cast<uint64_t*>(v2) + 16))();
    }
    
    // Clear the pointer
    *reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v1) + 40) = 0;  // *((_QWORD *)v1 + 5) = 0
}

} // namespace Rendering
} // namespace Authentication
} // namespace NexusPro
