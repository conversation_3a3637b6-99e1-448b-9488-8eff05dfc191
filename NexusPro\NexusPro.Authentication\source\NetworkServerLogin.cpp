/*
 * NexusPro Authentication Module
 * Network Server Login Implementation
 * 
 * Original Functions: Multiple network server login functions
 * Original Addresses: 0x1401C7250, 0x1401DA860
 * 
 * Purpose: Handles login processes for different network servers (Control and Web Agent)
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple network login functions
 * - Fixed malformed variable declarations
 * - Cleaned up global variable access
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/ServerConnections.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Network {

/**
 * @brief Control server login process
 * @note Original Function: ?LogInControllServer@CNetworkEX@@AEAA_NHPEAD@Z
 * @note Original Address: 0x1401C7250
 */
char __fastcall CNetworkEX_LogInControllServer(CNetworkEX* this, int n, char* pBuf)
{
    // Initialize debug pattern (original decompiled logic)
    uint64_t* v3 = reinterpret_cast<uint64_t*>(&this);
    for (int64_t i = 32; i > 0; --i) {
        *reinterpret_cast<uint32_t*>(v3) = 0xCCCCCCCC;  // Debug pattern
        v3 = reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v3) + 4);
    }
    
    char* v7 = pBuf;
    char pbyType = 54;  // Control server message type
    char v9 = 1;
    char szMsg = 0;
    
    // Check if control server is already logged in
    if (g_bControlServerLoggedIn) {
        szMsg = 1;
        CNetProcess::LoadSendMsg(g_pNetProcess, n, &pbyType, &szMsg, 1u);
        return 0;  // Already logged in
    } else if (static_cast<uint8_t>(*v7) == 239) {  // Expected login response code
        // Set control server as logged in
        g_bControlServerLoggedIn = true;
        g_nControlServerSocket = n;
        
        CNetProcess::LoadSendMsg(g_pNetProcess, n, &pbyType, &szMsg, 1u);
        return 1;  // Login successful
    } else {
        // Invalid response
        szMsg = 1;
        CNetProcess::LoadSendMsg(g_pNetProcess, n, &pbyType, &szMsg, 1u);
        return 0;  // Login failed
    }
}

/**
 * @brief Web agent server login process
 * @note Original Function: ?LogInWebAgentServer@CNetworkEX@@AEAA_NHPEAD@Z
 * @note Original Address: 0x1401DA860
 */
char __fastcall CNetworkEX_LogInWebAgentServer(CNetworkEX* this, int n, char* pBuf)
{
    // Initialize debug pattern (original decompiled logic)
    uint64_t* v3 = reinterpret_cast<uint64_t*>(&this);
    for (int64_t i = 32; i > 0; --i) {
        *reinterpret_cast<uint32_t*>(v3) = 0xCCCCCCCC;  // Debug pattern
        v3 = reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v3) + 4);
    }
    
    char* v7 = pBuf;
    char pbyType = 51;  // Web agent server message type
    char v9 = 1;
    char szMsg = 0;
    
    // Check if web agent server is already logged in
    if (g_bWebAgentServerLoggedIn) {
        szMsg = 1;
        CNetProcess::LoadSendMsg(g_pNetProcess, n, &pbyType, &szMsg, 1u);
        return 1;  // Already logged in
    } else if (static_cast<uint8_t>(*v7) == 237) {  // Expected login response code
        // Set web agent server as logged in
        g_bWebAgentServerLoggedIn = true;
        g_nWebAgentServerSocket = n;
        
        CNetProcess::LoadSendMsg(g_pNetProcess, n, &pbyType, &szMsg, 1u);
        return 1;  // Login successful
    } else {
        // Invalid response
        szMsg = 1;
        CNetProcess::LoadSendMsg(g_pNetProcess, n, &pbyType, &szMsg, 1u);
        return 0;  // Login failed
    }
}

} // namespace Network
} // namespace Authentication
} // namespace NexusPro
