/*
 * NexusPro Authentication Module
 * AsyncLog STL Hash Functions Header
 * 
 * Original Functions: Multiple STL hash container functions for AsyncLogInfo
 * Original Addresses: 0x1403C1910 - 0x1403C3080
 * 
 * Purpose: Header for STL hash container operations for AsyncLogInfo storage and retrieval
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

// Forward declarations for STL types
namespace std {
    template<class T> class less;
    template<class T> class allocator;
    template<class T1, class T2> struct pair;
    
    template<class T, class Alloc>
    class list {
    public:
        template<int N> class _Iterator;
        
        static _Iterator<0>* begin(list* this_list, _Iterator<0>* result);
        static _Iterator<0>* end(list* this_list, _Iterator<0>* result);
        static unsigned __int64 size(list* this_list);
        static _Iterator<0>* find(list* this_list, int key, _Iterator<0>* result);
    };
}

namespace stdext {
    template<class Key, class Compare> class hash_compare;
    
    template<class Key, class T, class Compare, class Alloc, int Multi>
    struct _Hmap_traits;
    
    template<class Traits>
    class _Hash {
    public:
        std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> _List;
        
        // Iterator type alias
        typedef std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> iterator;
        
        // Member functions
        iterator* begin(_Hash* this_hash, iterator* result);
        iterator* end(_Hash* this_hash, iterator* result);
        unsigned __int64 size(_Hash* this_hash);
        iterator* find(_Hash* this_hash, int _Keyval, iterator* result);
    };
}

// Forward declaration for CAsyncLogInfo
class CAsyncLogInfo;

/**
 * @brief STL Hash container function declarations for AsyncLogInfo
 * 
 * These functions provide standard STL hash container operations for
 * storing and retrieving CAsyncLogInfo objects by integer keys.
 */

// Hash container type alias for readability
typedef stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>> AsyncLogHashContainer;
typedef std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> AsyncLogIterator;

/**
 * @brief Returns iterator to beginning of hash container
 */
AsyncLogIterator *__fastcall AsyncLogHashContainer::begin(
    AsyncLogHashContainer *this, 
    AsyncLogIterator *result);

/**
 * @brief Returns iterator to end of hash container
 */
AsyncLogIterator *__fastcall AsyncLogHashContainer::end(
    AsyncLogHashContainer *this, 
    AsyncLogIterator *result);

/**
 * @brief Returns size of hash container
 */
unsigned __int64 __fastcall AsyncLogHashContainer::size(
    AsyncLogHashContainer *this);

/**
 * @brief Finds element in hash container
 */
AsyncLogIterator *__fastcall AsyncLogHashContainer::find(
    AsyncLogHashContainer *this,
    int _Keyval,
    AsyncLogIterator *result);

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
