/*
 * NexusPro Authentication Module
 * Session Map Destructors Header
 * 
 * Original Functions: Multiple dtor$* functions for _afxSessionMap
 * Original Addresses: 0x14057B120 - 0x14057C810
 * 
 * Purpose: Header for AFX session map and cryptographic parameters cleanup
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Session {

// Forward declarations for CryptoPP types
namespace CryptoPP {
    class ECP;
    class EC2N;
    
    template<class EC>
    class EcRecommendedParameters {
    public:
        void __fastcall ~EcRecommendedParameters(void *params);
    };
}

// External data references for cryptographic parameters
extern void* unk_184A89708;  // ECP parameters 0
extern void* unk_184A89710;  // ECP parameters 1
extern void* unk_184A89718;  // ECP parameters 2
extern void* unk_184A89720;  // ECP parameters 3
extern void* unk_184A89A00;  // EC2N parameters 31

/**
 * @brief Session map destructor functions
 * 
 * These functions clean up various cryptographic parameters used in the
 * AFX session map. They are called during application shutdown to ensure
 * proper cleanup of ECP and EC2N elliptic curve parameters.
 */

void __fastcall SessionMapDestructor0();   // ECP parameters cleanup
void __fastcall SessionMapDestructor1();   // Additional ECP cleanup
void __fastcall SessionMapDestructor2();   // ECP parameters variant 2
void __fastcall SessionMapDestructor3();   // ECP parameters variant 3
void __fastcall SessionMapDestructor31();  // EC2N parameters cleanup

} // namespace Session
} // namespace Authentication
} // namespace NexusPro
