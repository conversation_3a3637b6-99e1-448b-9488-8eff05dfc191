#include "../headers/GuildBattleLogin.h"

namespace RFOnline {
namespace Authentication {
namespace GuildBattle {

void GuildBattleLogin::HandleMemberLogin(
    CNormalGuildBattleGuild* guild,
    int memberIndex,
    unsigned int serialNumber,
    char battleNumber,
    char* destGuild,
    unsigned int playerID,
    CNormalGuildBattleField* battleField,
    CNormalGuildBattleLogger* logger)
{
    // Initialize local variables with debug pattern
    __int64 debugBuffer[20];
    for (int i = 0; i < 20; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    // Get member index from serial number
    int memberIdx = guild->GetMember(serialNumber);
    
    if (memberIdx >= 0) {
        // Member exists - handle login
        CPlayer* player = guild->m_kMember[memberIdx].GetPlayer();
        
        // Send position notifications
        guild->SendOhterNotifyCommitteeMemberPosition(player);
        guild->SendSelfNotifyCommitteeMemberPositionList(player);
        
        // Move member to battle field
        guild->MoveMember(memberIdx, playerID, battleField, logger);
        
        // Log the login event
        logger->Log(
            "CNormalGuildBattleGuild::LogIn( n(%d), dwSerial(%u), GuildBattleNumber(%u), %s, uiID(%u), pkField)",
            memberIndex,
            serialNumber,
            (unsigned int)battleNumber,
            destGuild,
            playerID
        );
    }
    else if ((unsigned char)battleNumber > guild->m_dwCurJoinMember) {
        // Member doesn't exist but can join - ask for join
        guild->AskJoin(memberIndex, destGuild, logger);
        
        // Log the join request
        logger->Log(
            "CNormalGuildBattleGuild::LogIn( n(%d), dwSerial(%u), GuildBattleNumber(%u), %s, uiID(%u), pkField) : Ask Join",
            memberIndex,
            serialNumber,
            (unsigned int)battleNumber,
            destGuild,
            playerID
        );
    }
}

void GuildBattleLogin::NotifyMemberPosition(
    CNormalGuildBattleGuild* guild,
    CPlayer* player)
{
    if (guild && player) {
        guild->SendOhterNotifyCommitteeMemberPosition(player);
        guild->SendSelfNotifyCommitteeMemberPositionList(player);
    }
}

void GuildBattleLogin::HandleJoinRequest(
    CNormalGuildBattleGuild* guild,
    int memberIndex,
    char* destGuild,
    CNormalGuildBattleLogger* logger)
{
    if (guild && destGuild && logger) {
        guild->AskJoin(memberIndex, destGuild, logger);
    }
}

void GuildBattleLogin::MoveMemberToBattle(
    CNormalGuildBattleGuild* guild,
    int memberIndex,
    unsigned int playerID,
    CNormalGuildBattleField* battleField,
    CNormalGuildBattleLogger* logger)
{
    if (guild && battleField && logger) {
        guild->MoveMember(memberIndex, playerID, battleField, logger);
    }
}

} // namespace GuildBattle
} // namespace Authentication
} // namespace RFOnline
