#include "../headers/AccountServerLogin.h"

// External symbol declarations for decompiled code
extern unsigned __int64 _security_cookie;
extern char g_cbHashVerify[32];
extern void* unk_1414F2090;

// External function declarations
extern unsigned int GetIPAddress();
extern void strcpy_0(char* dest, const char* src);
extern int strcmp_0(const char* str1, const char* str2);

namespace RFOnline {
namespace Authentication {
namespace Server {

unsigned short _open_world_request_wrac::size()
{
    return sizeof(_open_world_request_wrac);
}

void AccountServerLogin::ProcessLogin(CMainThread* mainThread)
{
    if (!mainThread) {
        return;
    }

    // Initialize debug buffer with pattern
    __int64 debugBuffer[92];
    for (int i = 0; i < 92; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    // Set up security cookie for stack protection
    unsigned __int64 stackCookie = (unsigned __int64)debugBuffer ^ _security_cookie;

    // Prepare world request structure
    _open_world_request_wrac worldRequest;

    // Copy world name to request
    strcpy_0(worldRequest.szWorldName, mainThread->m_szWorldName);

    // Get Gate IP from configuration file
    char ipBuffer[128];
    GetPrivateProfileStringA("System", "GateIP", "X", ipBuffer, 0x80u, "..\\WorldInfo\\WorldInfo.ini");

    // Determine IP address to use
    if (!strcmp_0(ipBuffer, "X")) {
        worldRequest.dwGateIP = GetIPAddress(); // Use local IP if not specified
    } else {
        worldRequest.dwGateIP = inet_addr(ipBuffer); // Use configured IP
    }

    // Copy hash verification data
    memcpy_s(worldRequest.cbHashVerify, 0x20ui64, g_cbHashVerify, 0x20ui64);

    // Set message type flags
    worldRequest.byType = 1;
    worldRequest.byFlag = 1;

    // Calculate message size and send network message
    unsigned short messageSize = _open_world_request_wrac::size();
    char messageType[2] = {1, 0}; // Message type 1

    // Send the world registration message
    CNetProcess::LoadSendMsg(
        unk_1414F2090,
        0,
        messageType,
        (char*)&worldRequest,
        messageSize
    );

    // Get nation setting manager instance and send cash DB DSN request
    CNationSettingManager* nationManager = CTSingleton<CNationSettingManager>::Instance();
    CNationSettingManager::SendCashDBDSNRequest(nationManager);
}

bool AccountServerLogin::PrepareWorldRequest(
    CMainThread* mainThread,
    _open_world_request_wrac* request)
{
    if (!mainThread || !request) {
        return false;
    }

    // Copy world name to request
    strcpy_0(request->szWorldName, mainThread->m_szWorldName);

    // Get Gate IP from configuration file
    char ipBuffer[128];
    GetPrivateProfileStringA("System", "GateIP", "X", ipBuffer, 0x80u, "..\\WorldInfo\\WorldInfo.ini");

    // Determine IP address to use
    if (!strcmp_0(ipBuffer, "X")) {
        request->dwGateIP = GetIPAddress(); // Use local IP if not specified
    } else {
        request->dwGateIP = inet_addr(ipBuffer); // Use configured IP
    }

    // Copy hash verification data
    memcpy_s(request->cbHashVerify, 0x20ui64, g_cbHashVerify, 0x20ui64);

    // Set message type flags
    request->byType = 1;
    request->byFlag = 1;

    return true;
}

unsigned int AccountServerLogin::GetGateIPAddress(char* ipBuffer, size_t bufferSize)
{
    if (!ipBuffer || bufferSize < 16) {
        return 0;
    }

    // Get Gate IP from configuration file
    GetPrivateProfileStringA("System", "GateIP", "X", ipBuffer, (DWORD)bufferSize, "..\\WorldInfo\\WorldInfo.ini");

    // Determine IP address to use
    if (!strcmp_0(ipBuffer, "X")) {
        return GetIPAddress(); // Use local IP if not specified
    } else {
        return inet_addr(ipBuffer); // Use configured IP
    }
}

bool AccountServerLogin::SendWorldRegistration(const _open_world_request_wrac* request)
{
    if (!request) {
        return false;
    }

    // Calculate message size
    unsigned short messageSize = _open_world_request_wrac::size();
    char messageType[2] = {1, 0}; // Message type 1

    // Send the world registration message
    CNetProcess::LoadSendMsg(
        unk_1414F2090,
        0,
        messageType,
        (char*)request,
        messageSize
    );

    return true;
}

void AccountServerLogin::InitializeHashVerification(char* hashBuffer, size_t bufferSize)
{
    if (!hashBuffer || bufferSize < 32) {
        return;
    }

    // Copy hash verification data
    memcpy_s(hashBuffer, bufferSize, g_cbHashVerify, 32);
}

void AccountServerLogin::SendCashDBDSNRequest()
{
    // Get nation setting manager instance and send cash DB DSN request
    CNationSettingManager* nationManager = CTSingleton<CNationSettingManager>::Instance();
    CNationSettingManager::SendCashDBDSNRequest(nationManager);
}

bool AccountServerLogin::ValidateWorldName(const char* worldName)
{
    if (!worldName) {
        return false;
    }

    // Check world name length
    size_t length = strlen(worldName);
    if (length < 1 || length > 33) {
        return false;
    }

    // Check for valid characters
    for (size_t i = 0; i < length; ++i) {
        char c = worldName[i];
        if (!((c >= 'a' && c <= 'z') ||
              (c >= 'A' && c <= 'Z') ||
              (c >= '0' && c <= '9') ||
              c == '_' || c == '-')) {
            return false;
        }
    }

    return true;
}

unsigned int AccountServerLogin::GetSystemIPAddress()
{
    return GetIPAddress();
}

bool AccountServerLogin::LoadWorldConfiguration(
    const char* configKey,
    const char* defaultValue,
    char* outputBuffer,
    size_t bufferSize)
{
    if (!configKey || !defaultValue || !outputBuffer || bufferSize < 1) {
        return false;
    }

    // Get configuration from WorldInfo.ini
    DWORD result = GetPrivateProfileStringA(
        "System",
        configKey,
        defaultValue,
        outputBuffer,
        (DWORD)bufferSize,
        "..\\WorldInfo\\WorldInfo.ini"
    );

    return result > 0;
}

} // namespace Server
} // namespace Authentication
} // namespace RFOnline
