/**
 * @file ValidateIntegerElement.h
 * @brief RF Online Integer-Based Group Element Validation Declarations
 * @note Original Function: ?ValidateElement@DL_GroupParameters_IntegerBased@CryptoPP@@UEBA_NIAEBVInteger@2@PEBV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@@Z
 * @note Original Address: 0x140630AE0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: ValidateElementDL_GroupParameters_IntegerBasedCryp_140630AE0.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
namespace CryptoPP {
    class DL_GroupParameters_IntegerBased;
    class Integer;
    template<class T> class DL_FixedBasePrecomputation;
}

/**
 * @namespace NexusPro::Authentication::Cryptography
 * @brief Cryptographic validation functions for RF Online authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Validates an element in an integer-based discrete logarithm group
 * @param groupParams Pointer to the integer-based group parameters
 * @param validationLevel Validation level (0=basic, 1=extended, 2=full)
 * @param element Pointer to the integer element to validate
 * @param precomputation Pointer to precomputed values for optimization (optional)
 * @return true if element is valid in the group, false otherwise
 *
 * This function performs comprehensive validation of an integer element within
 * a discrete logarithm group. The validation includes:
 * 
 * Level 0 (Basic): 
 * - Range validation (0 < element < prime for multiplicative groups)
 * - Non-negativity check (element >= 0 for additive groups)
 * - Basic group membership test
 * 
 * Level 1 (Extended):
 * - All basic checks
 * - Precomputation-based validation if available
 * - Optimized membership verification using precomputed values
 * 
 * Level 2 (Full):
 * - Jacobi symbol calculations for quadratic residue validation
 * - Advanced power calculations to verify group order
 * - Identity element verification through exponentiation
 * - Comprehensive cryptographic security validation
 * 
 * The function supports both multiplicative and additive group structures,
 * automatically detecting the group type and applying appropriate validation
 * methods. For performance optimization, precomputed values can be provided
 * to accelerate expensive modular exponentiation operations.
 * 
 * Jacobi Symbol Validation:
 * - For quadratic residue groups: (element^2 - 4) / prime should equal -1
 * - For multiplicative groups: element / prime should equal 1
 * 
 * @note Original Address: 0x140630AE0
 * @note Used for validating group elements in RF Online's authentication system
 * @note Critical for ensuring cryptographic security in discrete logarithm operations
 */
char ValidateIntegerBasedGroupElement(
    __int64 groupParams,
    unsigned int validationLevel,
    CryptoPP::Integer* element,
    __int64* precomputation
);

} // namespace Cryptography
} // namespace Authentication
} // namespace NexusPro
