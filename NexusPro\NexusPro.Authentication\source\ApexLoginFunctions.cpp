/**
 * @file ApexLoginFunctions.cpp
 * @brief RF Online Apex Login System Functions
 * @note Original Function: ?size@_apex_send_login@@QEAAHXZ
 * @note Original Address: 0x140410BF0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: size_apex_send_loginQEAAHXZ_140410BF0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

/**
 * @brief Returns the size of the apex send login structure
 * @param apexLogin Pointer to the apex send login instance
 * @return Size of the structure in bytes (13 bytes)
 *
 * This function returns the fixed size of the apex send login data structure
 * used for authentication communications with the Apex anti-cheat system.
 */
signed __int64 _apex_send_login::size(_apex_send_login* apexLogin) {
    // The apex send login structure has a fixed size of 13 bytes
    // This includes all necessary fields for authentication data transmission
    return 13;
}
