#pragma once

// England Billing Manager Functions Header
// Handles England-specific billing operations and cash queries
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Billing {

// Forward declarations
class CEnglandBillingMgr;
class CEngNetworkBillEX;
class CLogFile;

// Billing parameter structures
struct _param_cash_select {
    char* in_szAcc;     // Account name
    // Additional parameters would be defined here
};

// Receive data structure for billing operations
struct RECV_DATA {
    bool bResult;       // Operation result
    unsigned int dwSeq; // Sequence number
    unsigned short wType; // Message type
    void* pData;        // Data pointer

    // Constructor
    RECV_DATA();
    
    // Destructor
    ~RECV_DATA();
};

/**
 * England Billing Manager
 * Handles billing operations specific to England region
 */
class EnglandBillingMgr {
public:
    /**
     * Call RF Online authentication function for England billing
     * @param billingMgr Pointer to England billing manager
     * @param cashParam Pointer to cash selection parameters
     * @param index Operation index
     * @return 0 on success, 1 on failure
     */
    static int CallRFOnlineAuth(
        CEnglandBillingMgr* billingMgr,
        _param_cash_select* cashParam,
        int index
    );

    /**
     * Process cash query request
     * @param billingMgr Pointer to England billing manager
     * @param accountName Account name for query
     * @param index Query index
     * @return 0 on success, 1 on failure
     */
    static int ProcessCashQuery(
        CEnglandBillingMgr* billingMgr,
        const char* accountName,
        int index
    );

    /**
     * Format billing message
     * @param accountName Account name
     * @param index Message index
     * @param buffer Output buffer
     * @param bufferSize Buffer size
     * @return Length of formatted message
     */
    static int FormatBillingMessage(
        const char* accountName,
        int index,
        char* buffer,
        size_t bufferSize
    );

    /**
     * Create billing header
     * @param dataLength Length of data
     * @param buffer Output buffer
     * @param bufferSize Buffer size
     * @return Length of header
     */
    static int CreateBillingHeader(
        unsigned int dataLength,
        char* buffer,
        size_t bufferSize
    );

    /**
     * Send billing request
     * @param messageType Message type
     * @param messageData Message data
     * @param dataLength Data length
     * @return True if sent successfully, false otherwise
     */
    static bool SendBillingRequest(
        const char* messageType,
        const char* messageData,
        size_t dataLength
    );

    /**
     * Queue receive data
     * @param receiveData Pointer to receive data structure
     * @param sequenceNumber Sequence number
     * @param messageType Message type
     * @param paramData Parameter data
     */
    static void QueueReceiveData(
        RECV_DATA* receiveData,
        unsigned int sequenceNumber,
        unsigned short messageType,
        void* paramData
    );

    /**
     * Log billing operation
     * @param billingMgr Pointer to billing manager
     * @param message Log message
     * @param data Additional data to log
     */
    static void LogBillingOperation(
        CEnglandBillingMgr* billingMgr,
        const char* message,
        const char* data
    );
};

} // namespace Billing
} // namespace Authentication
} // namespace RFOnline
