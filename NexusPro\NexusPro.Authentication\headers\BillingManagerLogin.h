#pragma once

// Billing Manager Login Functions Header
// Handles billing system login operations
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Billing {

// Forward declarations
class CBillingManager;
class CBilling;
class CUserDB;

/**
 * Billing Manager Login Operations
 * Handles login operations for the billing system
 */
class BillingManagerLogin {
public:
    /**
     * Process billing manager login
     * @param billingManager Pointer to billing manager object
     * @param userDB Pointer to user database object
     */
    static void ProcessLogin(CBillingManager* billingManager, CUserDB* userDB);

    /**
     * Initialize billing login session
     * @param billingManager Pointer to billing manager object
     */
    static void InitializeSession(CBillingManager* billingManager);

    /**
     * Validate billing credentials
     * @param billingManager Pointer to billing manager object
     * @param userDB Pointer to user database object
     * @return True if credentials are valid, false otherwise
     */
    static bool ValidateCredentials(CBillingManager* billingManager, CUserDB* userDB);

    /**
     * Execute billing login procedure
     * @param billing Pointer to billing object
     */
    static void ExecuteLogin(CBilling* billing);
};

} // namespace Billing
} // namespace Authentication
} // namespace RFOnline
