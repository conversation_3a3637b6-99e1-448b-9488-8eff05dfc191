/*
 * NexusPro Authentication Module
 * Database Authentication Header
 * 
 * Original Functions: Multiple database authentication functions
 * Original Addresses: 0x140482430, 0x1404836A0
 * 
 * Purpose: Header for database authentication procedures for RF Online cash items and user verification
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Database {

/**
 * @brief RF Cash Item Database authentication procedure call
 * @param this Pointer to CRFCashItemDatabase instance
 * @param rParam Pointer to cash selection parameters
 * @return int64_t Authentication result (positive for success, negative for error)
 * @note Original Address: 0x140482430
 */
int64_t __fastcall CRFCashItemDatabase_CallProc_RFOnlineAuth(
    CRFCashItemDatabase* this, 
    _param_cash_select* rParam);

/**
 * @brief Japanese RF Cash Item Database authentication procedure call
 * @param this Pointer to CRFCashItemDatabase instance
 * @param rParam Pointer to cash selection parameters
 * @return int64_t Authentication result (positive for success, negative for error)
 * @note Original Address: 0x1404836A0
 */
int64_t __fastcall CRFCashItemDatabase_CallProc_RFOnlineAuth_Jap(
    CRFCashItemDatabase* this, 
    _param_cash_select* rParam);

} // namespace Database
} // namespace Authentication
} // namespace NexusPro
