/**
 * @file UnmannedTraderLoginComplete.h
 * @brief RF Online Unmanned Trader Login Completion Function Declarations
 * @note Original Function: ?CompleteLogInCompete@CUnmannedTraderController@@QEAAXPEAD@Z
 * @note Original Address: 0x14034EF80
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
class CUnmannedTraderController;

/**
 * @namespace NexusPro::Authentication::Trading
 * @brief Trading system functions related to authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Trading {

/**
 * @brief Completes the login competition process for unmanned trader
 * @param traderController Pointer to the unmanned trader controller instance
 * @param data Pointer to the data buffer containing login competition results
 *
 * This function processes the results of a login competition for unmanned traders,
 * logging the outcome and handling any database errors that occurred during the
 * registration process. It iterates through all registration entries and logs
 * detailed information about each trader's status.
 * 
 * **Data Buffer Structure**:
 * - **Offset 0-1**: Type value (WORD)
 * - **Offset 4-7**: Seller ID (DWORD)
 * - **Offset 8**: Success flag (BYTE, 0 = success)
 * - **Offset 9**: Return value (BYTE)
 * - **Offset 10-11**: Number of entries (WORD)
 * - **Offset 12+**: Entry data (16 bytes per entry)
 * 
 * **Entry Structure** (16 bytes each):
 * - **Offset 0**: Database error flag (BYTE)
 * - **Offset 1**: Validity flag (BYTE, 255 = invalid)
 * - **Offset 4-7**: Registration serial (DWORD)
 * - **Offset 8-11**: Buyer ID (DWORD)
 * - **Offset 12**: Update state (BYTE)
 * 
 * **Processing Logic**:
 * 1. **Validation**: Checks success flag at offset 8
 * 2. **Main Logging**: Logs type, index, and seller information
 * 3. **Entry Processing**: Iterates through all registration entries
 * 4. **Error Handling**: Logs database errors for failed entries
 * 5. **Status Tracking**: Records update states and process information
 * 
 * **Error Conditions**:
 * - Database connection failures during registration
 * - Invalid entry data (validity flag = 255)
 * - Update state conflicts
 * - Buyer/seller ID mismatches
 * 
 * **Logging Output**:
 * - Main completion status with type and seller info
 * - Detailed error logs for each failed entry
 * - Registration serial and buyer information
 * - Update state and process update flags
 * 
 * **Performance Considerations**:
 * - Processes variable number of entries efficiently
 * - Minimal memory allocation for stack operations
 * - Optimized logging to prevent performance bottlenecks
 * - Handles large trader registration datasets
 * 
 * @note Original Address: 0x14034EF80
 * @note Critical for unmanned trader system integrity
 * @note Ensures proper logging of all registration outcomes
 */
void CompleteUnmannedTraderLogin(CUnmannedTraderController* traderController, char* data);

} // namespace Trading
} // namespace Authentication
} // namespace NexusPro
