/*
 * NexusPro Authentication Module
 * AsyncLog Hash Map Constructor Implementation
 * 
 * Original Function: ??0?$hash_map@HPEAVCAsyncLogInfo@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@std@@@stdext@@QEAA@XZ
 * Original Address: 0x1403C17E0
 * 
 * Purpose: Constructor for STL hash_map used to store AsyncLogInfo objects by integer keys
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed STL template syntax
 * - Cleaned up variable naming
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/AsyncLogSTLAllocators.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Constructor for AsyncLogInfo hash map
 * @param this Pointer to the hash_map object being constructed
 * 
 * Initializes an STL hash_map that maps integer keys to CAsyncLogInfo pointers.
 * This is used for efficient lookup of logging information by ID.
 */
void __fastcall stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::hash_map(
    stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this)
{
    __int64 *v1; // rdi@1
    signed __int64 i; // rcx@1
    std::allocator<std::pair<int const,CAsyncLogInfo *>> *v3; // rax@4
    stdext::hash_compare<int,std::less<int>> *v4; // rax@4
    __int64 v5; // [sp+0h] [bp-38h]@1
    char v6; // [sp+20h] [bp-18h]@4
    char v7; // [sp+21h] [bp-17h]@4
    std::allocator<std::pair<int const,CAsyncLogInfo *>> *_Al; // [sp+28h] [bp-10h]@4
    stdext::hash_map<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *v9; // [sp+40h] [bp+8h]@1

    v9 = this;
    v1 = &v5;
    
    // Initialize debug pattern in local variables
    for (i = 12i64; i; --i) {
        *(_DWORD *)v1 = -858993460;
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Initialize allocator for pair<const int, CAsyncLogInfo*>
    std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator((std::allocator<std::pair<int const,CAsyncLogInfo *>> *)&v6);
    _Al = v3;
    
    // Initialize hash compare function object
    stdext::hash_compare<int,std::less<int>>::hash_compare((stdext::hash_compare<int,std::less<int>> *)&v7);
    
    // Initialize the underlying hash container
    stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>>::_Hash(
        (stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CAsyncLogInfo *>>,0>> *)&v9->_Myfirstiter,
        v4,
        _Al);
}

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
