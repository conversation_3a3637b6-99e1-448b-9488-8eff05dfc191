/*
 * NexusPro Authentication Module
 * Cryptographic EC2N Validation Implementation
 * 
 * Original Functions: Multiple EC2N validation functions
 * Original Addresses: 0x1405ADAD0, 0x1405ADAF0, 0x1405ADD90
 * 
 * Purpose: Elliptic Curve Binary Field (EC2N) parameter validation for cryptographic operations
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple EC2N validation functions
 * - Fixed malformed variable declarations
 * - Simplified complex virtual function calls
 * - Maintained original decompiled logic
 */

#include "../headers/CryptoValidation.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Crypto {

/**
 * @brief EC2N DL_GroupParameters validation thunk
 * @note Original Function: ?Validate@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@BBI@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x1405ADAD0
 */
int __fastcall EC2N_DL_GroupParameters_ValidateThunk(uint64_t a1)
{
    // This is a virtual function thunk that adjusts the 'this' pointer
    // and calls the actual validation function
    return CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::Validate(
        a1 - *reinterpret_cast<uint32_t*>(a1 - 4) - 280
    );
}

/**
 * @brief EC2N DL_GroupParameters validation implementation
 * @note Original Function: ?Validate@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x1405ADAF0
 */
char __fastcall EC2N_DL_GroupParameters_Validate(uint64_t a1, uint64_t a2, uint32_t a3)
{
    uint64_t v10 = a1;
    uint64_t v11 = a2;
    uint32_t v12 = a3;
    
    // Get virtual function pointer and call validation check
    int (***v3)() = reinterpret_cast<int (***)()>(
        (*reinterpret_cast<uint64_t**>(a1 - 32) + 6)  // Virtual function table offset
    );
    
    // Call virtual function to check if parameters are valid
    if (static_cast<uint8_t>((**v3)())) {
        // Check if current validation level is sufficient
        if (*reinterpret_cast<uint32_t*>(v10 - 16) <= v12) {
            // Perform comprehensive validation
            bool v8 = false;
            
            // Call base validation function
            bool baseValid = static_cast<uint8_t>(
                (*reinterpret_cast<int (**)(uint64_t, uint64_t, uint32_t)>(
                    *reinterpret_cast<uint64_t*>(v10 - 32) + 128
                ))(v10 - 32, v11, v12)
            );
            
            if (baseValid) {
                // Get curve parameters for additional validation
                uint64_t v5 = (*reinterpret_cast<int (**)(uint64_t)>(
                    *reinterpret_cast<uint64_t*>(v10 - 32) + 48
                ))(v10 - 32);
                
                uint64_t v6 = v5;
                
                uint64_t v7 = (*reinterpret_cast<int (**)(uint64_t)>(
                    *reinterpret_cast<uint64_t*>(v10 - 32) + 8
                ))(v10 - 32);
                
                // Validate curve equation and parameters
                v8 = static_cast<uint8_t>(
                    (*reinterpret_cast<int (**)(uint64_t, uint32_t, uint64_t, uint64_t)>(
                        *reinterpret_cast<uint64_t*>(v10 - 32) + 136
                    ))(v10 - 32, v12, v7, v6)
                );
            }
            
            // Update validation level based on result
            int v9;
            if (v8) {
                v9 = v12 + 1;  // Increase validation level on success
            } else {
                v9 = 0;        // Reset validation level on failure
            }
            
            *reinterpret_cast<uint32_t*>(v10 - 16) = v9;
            return v8;
        } else {
            // Already validated at sufficient level
            return 1;
        }
    } else {
        // Basic validation failed
        return 0;
    }
}

/**
 * @brief EC2N DL_GroupParameters validation with this pointer adjustment
 * @note Original Function: ?Validate@?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@CryptoPP@@$4PPPPPPPM@A@EBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x1405ADD90
 */
char __fastcall EC2N_DL_GroupParameters_ValidateAdjusted(uint64_t a1, uint64_t a2, uint32_t a3)
{
    // This is another virtual function thunk with different this pointer adjustment
    // Adjust the 'this' pointer and call the main validation function
    uint64_t adjustedThis = a1 - *reinterpret_cast<uint32_t*>(a1 - 4) - 280;
    
    return EC2N_DL_GroupParameters_Validate(adjustedThis, a2, a3);
}

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
