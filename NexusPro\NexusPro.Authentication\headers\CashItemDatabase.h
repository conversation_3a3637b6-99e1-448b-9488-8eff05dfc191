#pragma once

// Cash Item Database Functions Header
// Handles cash item database operations and authentication
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations for SQL types
typedef void* SQLHSTMT;
typedef short SQLRETURN;
typedef long SQLLEN;

namespace RFOnline {
namespace Authentication {
namespace Database {

// Forward declarations
class CRFNewDatabase;

// Cash selection parameter structure
struct _param_cash_select {
    char* in_szAcc;             // Account name (input)
    unsigned int out_dwCashAmount; // Cash amount (output)
    bool out_bResult;           // Operation result (output)
};

/**
 * RF Cash Item Database
 * Handles cash item database operations and authentication
 */
class CashItemDatabase {
private:
    // Virtual function table pointer
    void* vfptr;

    // Database connection properties
    SQLHSTMT m_hStmtSelect;     // SQL statement handle for select operations
    bool m_bSaveDBLog;          // Whether to save database logs

public:
    /**
     * Default constructor
     */
    CashItemDatabase();

    /**
     * Destructor
     */
    ~CashItemDatabase();

    /**
     * Initialize database connection
     * @param connectionString Database connection string
     * @param saveLog Whether to save database logs
     * @return True if initialization successful, false otherwise
     */
    bool Initialize(const char* connectionString, bool saveLog);

    /**
     * Process RF Online authentication
     * @param cashParam Cash selection parameters
     * @return 0 on success, 1 on error, 2 on no data
     */
    int ProcessRFOnlineAuth(_param_cash_select* cashParam);

    /**
     * Process RF Online authentication for Japan region
     * @param cashParam Cash selection parameters
     * @return 0 on success, 1 on error, 2 on no data
     */
    int ProcessRFOnlineAuthJapan(_param_cash_select* cashParam);

    /**
     * Process RF Online authentication for Korea region
     * @param cashParam Cash selection parameters
     * @return 0 on success, 1 on error, 2 on no data
     */
    int ProcessRFOnlineAuthKorea(_param_cash_select* cashParam);

    /**
     * Process RF Online authentication for Taiwan region
     * @param cashParam Cash selection parameters
     * @return 0 on success, 1 on error, 2 on no data
     */
    int ProcessRFOnlineAuthTaiwan(_param_cash_select* cashParam);

    /**
     * Process RF Online authentication for China region
     * @param cashParam Cash selection parameters
     * @return 0 on success, 1 on error, 2 on no data
     */
    int ProcessRFOnlineAuthChina(_param_cash_select* cashParam);

    /**
     * Get cash amount for account
     * @param accountName Account name
     * @return Cash amount
     */
    unsigned int GetCashAmount(const char* accountName);

    /**
     * Check if database connection is active
     * @return True if connected, false otherwise
     */
    bool IsConnected() const;

    /**
     * Reconnect to database
     * @return True if reconnection successful, false otherwise
     */
    bool Reconnect();

    /**
     * Close database connection
     */
    void Close();

    /**
     * Log database operation
     * @param message Log message
     */
    void Log(const char* message);

    /**
     * Log formatted database operation
     * @param format Format string
     * @param ... Additional arguments
     */
    void FmtLog(const char* format, ...);

    /**
     * Log error message
     * @param format Format string
     * @param ... Additional arguments
     */
    void ErrLog(const char* format, ...);
};

} // namespace Database
} // namespace Authentication
} // namespace RFOnline
