/**
 * @file NotifyLoginSetBuff.cpp
 * @brief RF Online Race Buff Login Notification Function
 * @note Original Function: ?NotifyLogInSetBuff@CRaceBuffInfoByHolyQuest@@AEAAXG@Z
 * @note Original Address: 0x1403B42D0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: NotifyLogInSetBuffCRaceBuffInfoByHolyQuestAEAAXGZ_1403B42D0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External global object (defined elsewhere in the codebase)
extern void* unk_1414F2088;  // Network process instance

/**
 * @brief Notifies client about race buff settings when user logs in
 * @param this Pointer to CRaceBuffInfoByHolyQuest instance
 * @param wUserInx User index for the client to notify
 * 
 * This function sends a network message to inform the client about active
 * race buffs from Holy Quest system when a user logs in. The message contains:
 * - Buff level information
 * - Buff data index
 * - Message type identifiers for proper client handling
 * 
 * Race buffs are temporary enhancements that affect all players of a specific
 * race and are typically activated through Holy Quest completion events.
 */
void __fastcall CRaceBuffInfoByHolyQuest::NotifyLogInSetBuff(
    CRaceBuffInfoByHolyQuest* this,
    unsigned __int16 wUserInx) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;                    // Original: v2 (rdi)
    signed __int64 loopCounter;                // Original: i (rcx)
    __int64 stackBuffer[28];                   // Original: v4 ([sp+0h] [bp-78h])
    char messageData;                          // Original: szMsg ([sp+34h] [bp-44h])
    __int16 buffIndex;                         // Original: v6 ([sp+35h] [bp-43h])
    __int16 padding;                           // Original: v7 ([sp+37h] [bp-41h])
    char messageType;                          // Original: pbyType ([sp+54h] [bp-24h])
    char subMessageType;                       // Original: v9 ([sp+55h] [bp-23h])
    CRaceBuffInfoByHolyQuest* buffInstance;    // Original: v10 ([sp+80h] [bp+8h])

    // Initialize local variables
    buffInstance = this;
    bufferPointer = stackBuffer;
    
    // Initialize stack buffer with debug pattern (0xCCCCCCCC)
    for (loopCounter = 28LL; loopCounter; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        bufferPointer = reinterpret_cast<__int64*>(reinterpret_cast<char*>(bufferPointer) + 4);
    }
    
    // Prepare message data for network transmission
    messageData = buffInstance->m_byLv;                    // Buff level
    buffIndex = buffInstance->m_pData->m_dwIndex;          // Buff data index
    padding = 0;                                           // Padding for alignment
    
    // Set message type identifiers
    messageType = 17;      // Main message type for buff notifications
    subMessageType = 25;   // Sub-type for race buff login notifications
    
    // Send the buff notification message to the client
    // This uses the network process to send a message containing:
    // - Message type (17, 25) for proper client-side handling
    // - Buff level and index for client to apply the appropriate effects
    CNetProcess::LoadSendMsg(
        unk_1414F2088,     // Network process instance
        wUserInx,          // Target user index
        &messageType,      // Message type buffer
        &messageData,      // Message data buffer
        5u);               // Message size (5 bytes)
}
