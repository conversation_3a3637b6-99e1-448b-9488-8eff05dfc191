/**
 * @file BillingJPLogin.cpp
 * @brief RF Online Japan Billing Login Function
 * @note Original Function: ?Login@CBillingJP@@UEAAXPEAVCUserDB@@@Z
 * @note Original Address: 0x14028E910
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <winsock2.h>

/**
 * @brief Initiates login process for Japan billing system
 * @param this Pointer to CBillingJP instance
 * @param pUserDB Pointer to user database containing login information
 * 
 * This function handles the login process for Japan billing authentication,
 * specifically checking if the user is from a PC Bang (internet cafe) and
 * processing the billing information accordingly. It validates the user's
 * billing status and sends appropriate login messages to the Japan billing system.
 */
void __fastcall CBillingJP::Login(CBillingJP* this, CUserDB* pUserDB) {
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;            // Original: v2 (rdi register)
    signed __int64 loopCounter;        // Original: i (rcx register)
    char* ipAddressString;             // Original: v4 (rax register)
    __int64 stackBuffer[24];           // Original: v5 (stack buffer [sp+0h] [bp-68h])
    __int16 billingType;               // Original: v6 ([sp+20h] [bp-48h])
    _SYSTEMTIME* endDatePtr;           // Original: v7 ([sp+28h] [bp-40h])
    int remainingTime;                 // Original: v8 ([sp+30h] [bp-38h])
    _SYSTEMTIME* endDateCopy;          // Original: v9 ([sp+40h] [bp-28h])
    char* cmsData;                     // Original: v10 ([sp+48h] [bp-20h])
    CBillingVtbl* virtualTable;        // Original: v11 ([sp+50h] [bp-18h])
    CBillingJP* billingInstance;       // Original: v12 ([sp+70h] [bp+8h])
    CUserDB* userDatabase;             // Original: v13 ([sp+78h] [bp+10h])

    // Initialize local variables
    userDatabase = pUserDB;
    billingInstance = this;
    bufferPointer = stackBuffer;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    for (loopCounter = 24LL; loopCounter; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        bufferPointer = reinterpret_cast<__int64*>(reinterpret_cast<char*>(bufferPointer) + 4);
    }
    
    // Check if user is from a PC Bang (internet cafe)
    // Japan billing system has special handling for PC Bang users
    if (pUserDB->m_BillingInfo.bIsPcBang) {
        // Set up billing information pointers for PC Bang user
        endDateCopy = &pUserDB->m_BillingInfo.stEndDate;
        cmsData = pUserDB->m_BillingInfo.szCMS;
        
        // Convert user's IP address to string format for logging/billing
        ipAddressString = inet_ntoa(reinterpret_cast<struct in_addr>(pUserDB->m_dwIP));
        
        // Get virtual function table and prepare billing data
        virtualTable = billingInstance->vfptr;
        remainingTime = userDatabase->m_BillingInfo.lRemainTime;
        endDatePtr = endDateCopy;
        billingType = userDatabase->m_BillingInfo.iType;
        
        // Call virtual function to send login message to Japan billing system
        // This calls the SendMsg_Login function through the virtual table
        // The function signature expects: (this, accountID, ipAddress, cmsData)
        reinterpret_cast<void (__fastcall *)(CBillingJP*, signed __int64, char*, char*)>(
            virtualTable->SendMsg_Login)(
                billingInstance,
                reinterpret_cast<signed __int64>(userDatabase->m_szAccountID),
                ipAddressString,
                cmsData);
        
        // Set billing status to indicate no logout required for PC Bang users
        // This is specific to Japan billing system behavior
        CUserDB::SetBillingNoLogout(userDatabase, 0);
    }
    // If not a PC Bang user, no special billing processing is needed
    // Regular users are handled through different billing pathways
}
