#include "../headers/LuaTinkerValidation.h"

// External Lua function declarations
extern "C" {
    const void* lua_topointer(lua_State* L, int index);
    int lua_gettop(lua_State* L);
}

namespace RFOnline {
namespace Authentication {
namespace Scripting {

namespace lua_tinker {

table_obj::table_obj()
    : m_L(nullptr), m_pointer(nullptr), m_index(0)
{
}

table_obj::table_obj(lua_State* L, const void* pointer, int index)
    : m_L(L), m_pointer(pointer), m_index(index)
{
}

table_obj::~table_obj()
{
    clear();
}

bool table_obj::validate()
{
    // Initialize debug buffer with pattern
    __int64 debugBuffer[12];
    for (int i = 0; i < 12; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    if (!m_pointer) {
        return false;
    }

    // Check if the pointer at current index matches our stored pointer
    const void* currentPointer = lua_topointer(m_L, m_index);
    if (m_pointer == currentPointer) {
        return true;
    }

    // If not found at current index, search through the entire stack
    int stackTop = lua_gettop(m_L);
    for (int i = 1; i <= stackTop; ++i) {
        const void* stackPointer = lua_topointer(m_L, i);
        if (m_pointer == stackPointer) {
            // Found the table at a different index, update our index
            m_index = i;
            return true;
        }
    }

    // Table not found in stack, invalidate pointer
    m_pointer = nullptr;
    return false;
}

lua_State* table_obj::getLuaState() const
{
    return m_L;
}

const void* table_obj::getPointer() const
{
    return m_pointer;
}

int table_obj::getIndex() const
{
    return m_index;
}

void table_obj::setLuaState(lua_State* L)
{
    m_L = L;
}

void table_obj::setPointer(const void* pointer)
{
    m_pointer = pointer;
}

void table_obj::setIndex(int index)
{
    m_index = index;
}

bool table_obj::isInitialized() const
{
    return m_L != nullptr && m_pointer != nullptr;
}

void table_obj::clear()
{
    m_L = nullptr;
    m_pointer = nullptr;
    m_index = 0;
}

bool table_obj::findInStack()
{
    if (!m_L || !m_pointer) {
        return false;
    }

    int stackTop = lua_gettop(m_L);
    for (int i = 1; i <= stackTop; ++i) {
        const void* stackPointer = lua_topointer(m_L, i);
        if (m_pointer == stackPointer) {
            m_index = i;
            return true;
        }
    }

    return false;
}

} // namespace lua_tinker

bool LuaTinkerValidation::ValidateTableObject(lua_tinker::table_obj* tableObj)
{
    if (!tableObj) {
        return false;
    }

    return tableObj->validate();
}

bool LuaTinkerValidation::InitializeValidation(lua_State* L)
{
    if (!L) {
        return false;
    }

    // Validation system initialization
    // This could include setting up error handlers, registering validation functions, etc.
    return true;
}

void LuaTinkerValidation::CleanupValidation(lua_State* L)
{
    if (!L) {
        return;
    }

    // Cleanup validation resources
    // This could include removing error handlers, cleaning up registered functions, etc.
}

bool LuaTinkerValidation::IsLuaStateValid(lua_State* L)
{
    if (!L) {
        return false;
    }

    // Basic Lua state validation
    // Check if we can get the stack top without errors
    try {
        lua_gettop(L);
        return true;
    }
    catch (...) {
        return false;
    }
}

} // namespace Scripting
} // namespace Authentication
} // namespace RFOnline
