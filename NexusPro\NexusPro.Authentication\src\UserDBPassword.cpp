#include "../headers/UserDBPassword.h"

// External function declarations from original decompiled code
extern void strcpy_0(char* dest, const char* src);

namespace RFOnline {
namespace Authentication {
namespace Database {

bool UserDBPassword::UpdateTrunkPassword(CUserDB* userDB, const char* newPassword)
{
    if (!userDB || !newPassword) {
        return false;
    }

    // Initialize debug buffer with pattern
    __int64 debugBuffer[12];
    for (int i = 0; i < 12; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    // Get pointer to trunk password field
    char* trunkPassword = userDB->m_AvatorData.dbTrunk.wszPasswd;
    
    // Copy new password to trunk password field
    strcpy_0(trunkPassword, newPassword);
    
    // Mark data as updated
    userDB->m_bDataUpdate = 1;
    
    return true;
}

bool UserDBPassword::ValidateTrunkPassword(CUserDB* userDB, const char* password)
{
    if (!userDB || !password) {
        return false;
    }

    // Compare provided password with stored trunk password
    return strcmp(userDB->m_AvatorData.dbTrunk.wszPasswd, password) == 0;
}

void UserDBPassword::InitializeTrunkPassword(CUserDB* userDB)
{
    if (!userDB) {
        return;
    }

    // Clear trunk password
    memset(userDB->m_AvatorData.dbTrunk.wszPasswd, 0, 
           sizeof(userDB->m_AvatorData.dbTrunk.wszPasswd));
    
    // Mark data as updated
    userDB->m_bDataUpdate = 1;
}

void UserDBPassword::ClearTrunkPassword(CUserDB* userDB)
{
    if (!userDB) {
        return;
    }

    // Clear trunk password
    memset(userDB->m_AvatorData.dbTrunk.wszPasswd, 0, 
           sizeof(userDB->m_AvatorData.dbTrunk.wszPasswd));
    
    // Mark data as updated
    userDB->m_bDataUpdate = 1;
}

bool UserDBPassword::HasTrunkPassword(CUserDB* userDB)
{
    if (!userDB) {
        return false;
    }

    // Check if trunk password is set (not empty)
    return strlen(userDB->m_AvatorData.dbTrunk.wszPasswd) > 0;
}

size_t UserDBPassword::GetMaxPasswordLength()
{
    // Return maximum password length based on typical trunk password field size
    return 32; // Typical maximum password length
}

bool UserDBPassword::ValidatePasswordFormat(const char* password)
{
    if (!password) {
        return false;
    }

    size_t length = strlen(password);
    
    // Check minimum and maximum length
    if (length < 4 || length > GetMaxPasswordLength()) {
        return false;
    }

    // Check for valid characters (alphanumeric and common symbols)
    for (size_t i = 0; i < length; ++i) {
        char c = password[i];
        if (!((c >= 'a' && c <= 'z') || 
              (c >= 'A' && c <= 'Z') || 
              (c >= '0' && c <= '9') || 
              c == '_' || c == '-' || c == '.' || c == '@')) {
            return false;
        }
    }

    return true;
}

} // namespace Database
} // namespace Authentication
} // namespace RFOnline
