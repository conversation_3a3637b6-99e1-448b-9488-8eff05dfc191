/*
 * NexusPro Authentication Module
 * Network Server Login Header
 * 
 * Original Functions: Multiple network server login functions
 * Original Addresses: 0x1401C7250, 0x1401DA860
 * 
 * Purpose: Header for login processes for different network servers (Control and Web Agent)
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Network {

/**
 * @brief Control server login process
 * @param this Pointer to CNetworkEX instance
 * @param n Socket/connection identifier
 * @param pBuf Buffer containing login response data
 * @return char 1 if login successful, 0 otherwise
 * @note Original Address: 0x1401C7250
 */
char __fastcall CNetworkEX_LogInControllServer(CNetworkEX* this, int n, char* pBuf);

/**
 * @brief Web agent server login process
 * @param this Pointer to CNetworkEX instance
 * @param n Socket/connection identifier
 * @param pBuf Buffer containing login response data
 * @return char 1 if login successful, 0 otherwise
 * @note Original Address: 0x1401DA860
 */
char __fastcall CNetworkEX_LogInWebAgentServer(CNetworkEX* this, int n, char* pBuf);

} // namespace Network
} // namespace Authentication
} // namespace NexusPro
