/*
 * NexusPro Authentication Module
 * Unmanned Trader User Info Implementation
 * 
 * Original Function: ?IsLogInState@CUnmannedTraderUserInfo@@QEAA_NXZ
 * Original Address: 0x140366F20
 * 
 * Purpose: Manages unmanned trader user information and login state
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed variable declarations
 * - Cleaned up state checking logic
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/CryptoValidation.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Trading {

/**
 * @brief Checks if the unmanned trader user is in logged-in state
 * @param this Pointer to CUnmannedTraderUserInfo instance
 * @return bool Returns true if user is logged in, false otherwise
 * 
 * This function checks the current state of an unmanned trader user to determine
 * if they are currently logged into the system. Unmanned traders are automated
 * trading systems that can operate without direct user interaction.
 * 
 * State values:
 * - 0: Logged out / Disconnected
 * - 1: Logged in / Active
 * - Other values: Various intermediate states
 */
bool __fastcall CUnmannedTraderUserInfo::IsLogInState(CUnmannedTraderUserInfo *this)
{
    int *v1; // rdi@1
    signed __int64 i; // rcx@1
    int v4; // [sp+0h] [bp-18h]@1
    CUnmannedTraderUserInfo *v5; // [sp+20h] [bp+8h]@1

    v5 = this;
    v1 = &v4;
    
    // Initialize debug pattern in local variables
    for (i = 4i64; i; --i) {
        *v1 = -858993460;
        ++v1;
    }
    
    // Check if state equals 1 (logged in state)
    return v5->m_eState == 1;
}

} // namespace Trading
} // namespace Authentication
} // namespace NexusPro
