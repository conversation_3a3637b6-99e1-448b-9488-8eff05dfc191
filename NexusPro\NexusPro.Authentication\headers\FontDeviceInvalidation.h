/**
 * @file FontDeviceInvalidation.h
 * @brief RF Online Font Device Invalidation Function Declarations
 * @note Original Function: ?InvalidateDeviceObjects@CR3Font@@QEAAJXZ
 * @note Original Address: 0x140528820
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
class CR3Font;

/**
 * @namespace NexusPro::Authentication::Graphics
 * @brief Graphics system functions related to authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Graphics {

/**
 * @brief Invalidates device objects for R3 font rendering
 * @param r3Font Pointer to the R3 font instance
 * @return 0 on success
 *
 * This function invalidates and releases device-dependent objects associated
 * with R3 font rendering. It's called when the graphics device is lost or
 * needs to be reset, ensuring proper cleanup of font-related resources.
 * 
 * **Invalidation Process**:
 * 1. **Texture Release**: Releases font texture resources (texture 1 and 2)
 * 2. **Flag Clearing**: Clears texture validity flags
 * 3. **Private Release**: Calls internal font resource cleanup
 * 4. **Memory Management**: Ensures no memory leaks during device transitions
 * 
 * **Font Resources Managed**:
 * - Font texture atlases containing glyph bitmaps
 * - Device-dependent rendering surfaces
 * - GPU memory allocations for font data
 * - Vertex buffers for text rendering
 * 
 * **When to Call**:
 * - Graphics device lost scenarios
 * - Device reset operations
 * - Font system reinitialization
 * - Memory pressure cleanup
 * - Application shutdown
 * 
 * **Resource Management**:
 * - Uses proper COM reference counting for D3D objects
 * - Prevents double-release through flag management
 * - Ensures clean state for device recreation
 * - Maintains font system integrity during transitions
 * 
 * **Performance Considerations**:
 * - Relatively lightweight operation
 * - Should be called before device reset
 * - Font textures will need regeneration after device restore
 * - May cause temporary text rendering interruption
 * 
 * @note Original Address: 0x140528820
 * @note Critical for proper font system device lifecycle management
 * @note Ensures clean font resource cleanup during device transitions
 */
__int64 InvalidateFontDeviceObjects(CR3Font* r3Font);

} // namespace Graphics
} // namespace Authentication
} // namespace NexusPro
