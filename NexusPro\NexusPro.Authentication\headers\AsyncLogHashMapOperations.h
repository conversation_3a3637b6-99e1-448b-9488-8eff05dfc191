/*
 * NexusPro Authentication Module
 * AsyncLog Hash Map Operations Header
 * 
 * Original Functions: Multiple hash map operations for AsyncLogInfo containers
 * Original Addresses: 0x1403C2EC0, 0x1403C4520, etc.
 * 
 * Purpose: Header for hash map operations for AsyncLogInfo storage and retrieval
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/STLTypes.h"
#include "AsyncLogInfo.h"

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Hash map constructor with traits and allocator
 * @param this Pointer to hash map instance
 * @param parg Hash compare object
 * @param al Allocator for pairs
 * @note Original Address: 0x1403C2EC0
 */
void __fastcall AsyncLogHashMapConstructor(
    stdext::_Hash<stdext::_Hmap_traits<int, CAsyncLogInfo*, 
                                       stdext::hash_compare<int, std::less<int>>, 
                                       std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>>* this,
    const stdext::hash_compare<int, std::less<int>>* parg,
    const std::allocator<std::pair<int const, CAsyncLogInfo*>>* al);

/**
 * @brief Hash map traits constructor
 * @param this Pointer to hash map traits instance
 * @param comp Hash compare object
 * @note Original Address: 0x1403C4520
 */
void __fastcall AsyncLogHashMapTraitsConstructor(
    stdext::_Hmap_traits<int, CAsyncLogInfo*, 
                        stdext::hash_compare<int, std::less<int>>, 
                        std::allocator<std::pair<int const, CAsyncLogInfo*>>, 0>* this,
    const stdext::hash_compare<int, std::less<int>>* comp);

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
