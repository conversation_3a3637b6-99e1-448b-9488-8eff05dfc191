#pragma once

// Lua Tinker Validation Functions Header
// Handles Lua table object validation for authentication scripts
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations for Lua
struct lua_State;

namespace RFOnline {
namespace Authentication {
namespace Scripting {

namespace lua_tinker {

/**
 * Lua Table Object
 * Handles Lua table validation and pointer management
 */
class table_obj {
private:
    lua_State* m_L;         // Lua state pointer
    const void* m_pointer;  // Table pointer
    int m_index;            // Table index in Lua stack

public:
    /**
     * Default constructor
     */
    table_obj();

    /**
     * Constructor with parameters
     * @param L Lua state
     * @param pointer Table pointer
     * @param index Table index
     */
    table_obj(lua_State* L, const void* pointer, int index);

    /**
     * Destructor
     */
    ~table_obj();

    /**
     * Validate table object
     * Checks if the table pointer is still valid in the Lua state
     * @return True if valid, false otherwise
     */
    bool validate();

    /**
     * Get Lua state
     * @return Lua state pointer
     */
    lua_State* getLuaState() const;

    /**
     * Get table pointer
     * @return Table pointer
     */
    const void* getPointer() const;

    /**
     * Get table index
     * @return Table index
     */
    int getIndex() const;

    /**
     * Set Lua state
     * @param L Lua state
     */
    void setLuaState(lua_State* L);

    /**
     * Set table pointer
     * @param pointer Table pointer
     */
    void setPointer(const void* pointer);

    /**
     * Set table index
     * @param index Table index
     */
    void setIndex(int index);

    /**
     * Check if table object is initialized
     * @return True if initialized, false otherwise
     */
    bool isInitialized() const;

    /**
     * Clear table object
     */
    void clear();

    /**
     * Find table in Lua stack
     * Searches for the table pointer in the Lua stack and updates index
     * @return True if found, false otherwise
     */
    bool findInStack();
};

} // namespace lua_tinker

/**
 * Lua Tinker Validation System
 * Provides validation utilities for Lua integration
 */
class LuaTinkerValidation {
public:
    /**
     * Validate table object
     * @param tableObj Pointer to table object
     * @return True if valid, false otherwise
     */
    static bool ValidateTableObject(lua_tinker::table_obj* tableObj);

    /**
     * Initialize Lua validation system
     * @param L Lua state
     * @return True if initialized successfully, false otherwise
     */
    static bool InitializeValidation(lua_State* L);

    /**
     * Cleanup Lua validation system
     * @param L Lua state
     */
    static void CleanupValidation(lua_State* L);

    /**
     * Check Lua state validity
     * @param L Lua state
     * @return True if valid, false otherwise
     */
    static bool IsLuaStateValid(lua_State* L);
};

} // namespace Scripting
} // namespace Authentication
} // namespace RFOnline
