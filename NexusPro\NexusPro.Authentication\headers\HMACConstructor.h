/*
 * NexusPro Authentication Module
 * HMAC Constructor Header
 * 
 * Original Function: ??0?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Original Address: 0x140465820
 * 
 * Purpose: Header for CryptoPP HMAC-SHA1 message authentication code implementation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Crypto {

// Forward declarations for CryptoPP types
namespace CryptoPP {
    class HMAC_Base;
    template<class T> class HMAC;
    class SHA1;
    
    struct ClonableVtbl;
    struct SimpleKeyingInterfaceVtbl;
    
    template<class BASE, class ALGORITHM>
    class SimpleKeyingInterfaceImpl;
    
    template<class INTERFACE, class ALGORITHM>
    class AlgorithmImpl;
    
    template<class BASE, class ALGORITHM>
    class MessageAuthenticationCodeImpl {
    public:
        union {
            ClonableVtbl *vfptr;
            SimpleKeyingInterfaceVtbl *vfptr_keying;
        };
        
        // Constructor
        void __fastcall MessageAuthenticationCodeImpl(
            MessageAuthenticationCodeImpl<BASE, ALGORITHM> *this);
    };
}

/**
 * @brief Constructor for HMAC-SHA1 Message Authentication Code Implementation
 * @param this Pointer to the object being constructed
 * 
 * Initializes the CryptoPP HMAC-SHA1 implementation with proper virtual table setup
 * and algorithm initialization. This is critical for cryptographic authentication
 * in the RF Online security system.
 */
void __fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::MessageAuthenticationCodeImpl(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>> *this);

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
