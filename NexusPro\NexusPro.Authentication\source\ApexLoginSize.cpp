/*
 * NexusPro Authentication Module
 * Apex Login Size Functions Implementation
 * 
 * Original Functions: Apex login size functions
 * Original Addresses: 0x140410BF0, 0x140013B97
 * 
 * Purpose: Provides size information for Apex anti-cheat login packets
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated Apex size functions
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/ApexLoginFunctions.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Apex {

/**
 * @brief Get size of Apex send login packet
 * @note Original Function: ?size@_apex_send_login@@QEAAHXZ
 * @note Original Address: 0x140410BF0
 */
int64_t __fastcall apex_send_login_size(_apex_send_login* this)
{
    // Return fixed size for Apex login packet
    return 13;  // Original: 13i64
}

/**
 * @brief Jump table wrapper for Apex send login size
 * @note Original Function: j_?size@_apex_send_login@@QEAAHXZ
 * @note Original Address: 0x140013B97
 */
int __fastcall j_apex_send_login_size(_apex_send_login* this)
{
    // Call the main size function
    return static_cast<int>(apex_send_login_size(this));
}

} // namespace Apex
} // namespace Authentication
} // namespace NexusPro
