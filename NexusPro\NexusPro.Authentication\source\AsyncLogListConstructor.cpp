/*
 * NexusPro Authentication Module
 * AsyncLog List Constructor Implementation
 * 
 * Original Function: ??0?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEAA@AEBV?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@1@@Z
 * Original Address: 0x1403C45B0
 * 
 * Purpose: Constructor for STL list used to store AsyncLogInfo pairs
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed STL template syntax
 * - Cleaned up variable naming
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/AsyncLogSTLAllocators.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Constructor for AsyncLogInfo list with allocator
 * @param this Pointer to the list object being constructed
 * @param _Al Allocator to use for memory management
 * 
 * Initializes an STL list that stores pairs of (int, CAsyncLogInfo*).
 * This is used for ordered storage of logging information.
 */
void __fastcall std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::list(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *this, 
    std::allocator<std::pair<int const,CAsyncLogInfo *>> *_Al)
{
    __int64 *v2; // rdi@1
    signed __int64 i; // rcx@1
    std::allocator<std::pair<int const,CAsyncLogInfo *>> v4; // al@4
    __int64 v5; // [sp+0h] [bp-38h]@1
    char v6; // [sp+20h] [bp-18h]@4
    std::allocator<std::pair<int const,CAsyncLogInfo *>> *v7; // [sp+28h] [bp-10h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *v8; // [sp+40h] [bp+8h]@1

    v8 = this;
    v2 = &v5;
    
    // Initialize debug pattern in local variables
    for (i = 12i64; i; --i) {
        *(_DWORD *)v2 = -858993460;
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    // Copy construct the allocator
    v7 = (std::allocator<std::pair<int const,CAsyncLogInfo *>> *)&v6;
    std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator(
        (std::allocator<std::pair<int const,CAsyncLogInfo *>> *)&v6,
        _Al);
    
    // Initialize the list value container
    std::_List_val<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_List_val(
        (std::_List_val<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> *)&v8->_Myfirstiter,
        v4);
    
    // Buy the head node for the list
    v8->_Myhead = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Buynode(v8);
    
    // Initialize head node pointers to point to itself (empty list)
    v8->_Myhead->_Next = v8->_Myhead;
    v8->_Myhead->_Prev = v8->_Myhead;
    v8->_Mysize = 0;
}

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
