/*
 * NexusPro Authentication Module
 * Apex Login Size Functions Header
 * 
 * Original Functions: Apex login size functions
 * Original Addresses: 0x140410BF0, 0x140013B97
 * 
 * Purpose: Header for size information for Apex anti-cheat login packets
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Apex {

/**
 * @brief Get size of Apex send login packet
 * @param this Pointer to _apex_send_login instance
 * @return int64_t Size of the login packet (13 bytes)
 * @note Original Address: 0x140410BF0
 */
int64_t __fastcall apex_send_login_size(_apex_send_login* this);

/**
 * @brief Jump table wrapper for Apex send login size
 * @param this Pointer to _apex_send_login instance
 * @return int Size of the login packet (13 bytes)
 * @note Original Address: 0x140013B97
 */
int __fastcall j_apex_send_login_size(_apex_send_login* this);

} // namespace Apex
} // namespace Authentication
} // namespace NexusPro
