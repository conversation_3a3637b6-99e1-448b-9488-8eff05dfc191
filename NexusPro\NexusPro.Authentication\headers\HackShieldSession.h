#pragma once

// HackShield Session Management Functions Header
// Handles HackShield session connect, disconnect, and loop operations
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Security {

// Forward declarations
class CHackShieldExSystem;
class CUserDB;

// Base HackShield parameter structure
struct BASE_HACKSHEILD_PARAM {
    // Virtual function table
    struct VTable {
        void (__fastcall *OnConnect)(BASE_HACKSHEILD_PARAM* param, unsigned int sessionId);
        void (__fastcall *OnDisConnect)(BASE_HACKSHEILD_PARAM* param);
        void (__fastcall *OnLoop)(BASE_HACKSHEILD_PARAM* param);
        bool (__fastcall *IsLogPass)(BASE_HACKSHEILD_PARAM* param);
    } *vfptr;

    // Parameter data
    int m_sessionId;
    bool m_bActive;
    bool m_bLogPass;
};

/**
 * HackShield Session Management
 * Handles session operations for HackShield anti-cheat system
 */
class HackShieldSession {
public:
    /**
     * Handle session connect event
     * @param hackShieldSystem Pointer to HackShield system
     * @param sessionId Session ID
     */
    static void OnConnectSession(CHackShieldExSystem* hackShieldSystem, int sessionId);

    /**
     * Handle session disconnect event
     * @param hackShieldSystem Pointer to HackShield system
     * @param sessionId Session ID
     */
    static void OnDisConnectSession(CHackShieldExSystem* hackShieldSystem, int sessionId);

    /**
     * Handle session loop event
     * @param hackShieldSystem Pointer to HackShield system
     * @param sessionId Session ID
     */
    static void OnLoopSession(CHackShieldExSystem* hackShieldSystem, int sessionId);

    /**
     * Get HackShield parameter for session
     * @param hackShieldSystem Pointer to HackShield system
     * @param sessionId Session ID
     * @return Pointer to HackShield parameter, nullptr if not found
     */
    static BASE_HACKSHEILD_PARAM* GetSessionParam(
        CHackShieldExSystem* hackShieldSystem,
        int sessionId
    );

    /**
     * Initialize session parameter
     * @param param Pointer to parameter structure
     * @param sessionId Session ID
     */
    static void InitializeSessionParam(BASE_HACKSHEILD_PARAM* param, int sessionId);

    /**
     * Cleanup session parameter
     * @param param Pointer to parameter structure
     */
    static void CleanupSessionParam(BASE_HACKSHEILD_PARAM* param);

    /**
     * Check if session is active
     * @param sessionId Session ID
     * @return True if session is active, false otherwise
     */
    static bool IsSessionActive(int sessionId);

    /**
     * Check if session has log pass
     * @param param Pointer to parameter structure
     * @return True if session has log pass, false otherwise
     */
    static bool IsSessionLogPass(BASE_HACKSHEILD_PARAM* param);

    /**
     * Get user database for session
     * @param sessionId Session ID
     * @return Pointer to user database, nullptr if not found
     */
    static CUserDB* GetUserDatabase(int sessionId);

    /**
     * Validate session ID
     * @param sessionId Session ID
     * @return True if valid, false otherwise
     */
    static bool ValidateSessionId(int sessionId);
};

} // namespace Security
} // namespace Authentication
} // namespace RFOnline
