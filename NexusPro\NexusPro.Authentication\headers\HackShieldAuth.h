#pragma once

// HackShield Authentication Functions Header
// Handles HackShield anti-cheat authentication and verification
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Security {

// Forward declarations
class HACKSHEILD_PARAM_ANTICP;
class CNetProcess;

// HackShield message types
enum class HackShieldMessageType : char {
    CHECKSUM_REQUEST = 98,
    CHECKSUM_RESPONSE = 2
};

// HackShield verification states
enum class HackShieldVerifyState : char {
    UNVERIFIED = 0,
    PENDING = 1,
    VERIFIED = 2,
    FAILED = 3
};

/**
 * HackShield Authentication System
 * Handles anti-cheat verification and checksum validation
 */
class HackShieldAuth {
public:
    /**
     * Process server checksum request
     * @param hackShield Pointer to HackShield parameter object
     * @param clientIndex Client socket index
     * @return True if request processed successfully, false otherwise
     */
    static bool ProcessServerChecksumRequest(
        HACKSHEILD_PARAM_ANTICP* hackShield,
        int clientIndex
    );

    /**
     * Initialize HackShield parameters
     * @param hackShield Pointer to HackShield parameter object
     */
    static void InitializeHackShield(HACKSHEILD_PARAM_ANTICP* hackShield);

    /**
     * Generate GUID request message
     * @param buffer Output buffer for message
     * @param guidInfo Client GUID information
     * @return Error code (0 = success)
     */
    static unsigned int GenerateGuidRequest(
        char* buffer,
        const char* guidInfo
    );

    /**
     * Send checksum request to client
     * @param clientIndex Client socket index
     * @param messageType Message type
     * @param messageData Message data buffer
     * @param dataSize Size of message data
     */
    static void SendChecksumRequest(
        int clientIndex,
        char messageType,
        const char* messageData,
        unsigned int dataSize
    );

    /**
     * Handle verification failure
     * @param hackShield Pointer to HackShield parameter object
     * @param errorType Error type
     * @param errorCode Error code
     */
    static void HandleVerificationFailure(
        HACKSHEILD_PARAM_ANTICP* hackShield,
        int errorType,
        unsigned int errorCode
    );

    /**
     * Set client verification state
     * @param hackShield Pointer to HackShield parameter object
     * @param clientIndex Client socket index
     * @param state Verification state
     */
    static void SetVerificationState(
        HACKSHEILD_PARAM_ANTICP* hackShield,
        int clientIndex,
        HackShieldVerifyState state
    );
};

} // namespace Security
} // namespace Authentication
} // namespace RFOnline
