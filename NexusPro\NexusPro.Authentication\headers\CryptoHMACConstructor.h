/*
 * NexusPro Authentication Module
 * Cryptographic HMAC Constructor Header
 * 
 * Original Function: ??0?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Original Address: 0x140465820
 * 
 * Purpose: Header for CryptoPP HMAC message authentication code implementation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/CryptoPPTypes.h"

namespace NexusPro {
namespace Authentication {
namespace Crypto {

/**
 * @brief HMAC MessageAuthenticationCodeImpl constructor
 * @param this Pointer to MessageAuthenticationCodeImpl instance
 * @note Original Address: 0x140465820
 */
void __fastcall MessageAuthenticationCodeImpl_HMAC_Constructor(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base, CryptoPP::HMAC<CryptoPP::SHA1>>* this);

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
