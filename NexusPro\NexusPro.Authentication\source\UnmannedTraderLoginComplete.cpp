/**
 * @file UnmannedTraderLoginComplete.cpp
 * @brief RF Online Unmanned Trader Login Completion Functions
 * @note Original Function: ?CompleteLogInCompete@CUnmannedTraderController@@QEAAXPEAD@Z
 * @note Original Address: 0x14034EF80
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: CompleteLogInCompeteCUnmannedTraderControllerQEAAX_14034EF80.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

/**
 * @brief Completes the login competition process for unmanned trader
 * @param traderController Pointer to the unmanned trader controller instance
 * @param data Pointer to the data buffer containing login competition results
 *
 * This function processes the results of a login competition for unmanned traders,
 * logging the outcome and handling any database errors that occurred during the
 * registration process. It iterates through all registration entries and logs
 * detailed information about each trader's status.
 */
void CUnmannedTraderController::CompleteLogInCompete(
    CUnmannedTraderController* traderController,
    char* data) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;                    // Original: v2 (rdi register)
    signed __int64 loopCounter;                // Original: i (rcx register)
    unsigned int typeValue;                    // Original: v4 (eax register)
    unsigned int returnValue;                  // Original: v5 (ecx register)
    int updateState;                           // Original: v6 (ecx register)
    __int64 stackBuffer;                       // Original: v7 ([sp+0h] [bp-58h])
    int sellerValue;                           // Original: v8 ([sp+20h] [bp-38h])
    int procUpdate;                            // Original: v9 ([sp+28h] [bp-30h])
    int buyerValue;                            // Original: v10 ([sp+30h] [bp-28h])
    char* currentData;                         // Original: v11 ([sp+40h] [bp-18h])
    unsigned int entryIndex;                   // Original: j ([sp+48h] [bp-10h])
    CUnmannedTraderController* currentController; // Original: v13 ([sp+60h] [bp+8h])

    // Initialize parameters
    currentController = traderController;
    
    // Initialize stack buffer with debug pattern
    bufferPointer = &stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC; // -858993460
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Set current data pointer
    currentData = data;
    
    // Check if the operation was successful (byte at offset 8 should be 0)
    if (!data[8]) {
        // Extract basic information from the data buffer
        typeValue = *reinterpret_cast<WORD*>(currentData);           // Type at offset 0
        returnValue = static_cast<unsigned char>(currentData[9]);    // Return value at offset 9
        sellerValue = *reinterpret_cast<DWORD*>(currentData + 4);    // Seller ID at offset 4
        
        // Log the main completion information
        CUnmannedTraderController::Log(
            currentController,
            "CUnmannedTraderController::CompleteLogInCompete( BYTE byRet, char * pLoadData )\r\n"
            "\t\tType(%u) wInx(%u) dwSeller(%u)\r\n",
            returnValue,
            typeValue,
            sellerValue
        );
        
        // Process each registration entry in the data buffer
        // The number of entries is stored at offset 10 (WORD)
        for (entryIndex = 0; 
             static_cast<int>(entryIndex) < *reinterpret_cast<WORD*>(currentData + 10); 
             ++entryIndex) {
            
            // Calculate the offset for this entry (16 bytes per entry, starting at offset 12)
            int entryOffset = 16 * entryIndex + 12;
            
            // Check if this entry is valid (byte at offset 1 of entry should not be 255)
            if (static_cast<unsigned char>(currentData[entryOffset + 1]) != 255) {
                
                // Check if there was a database error (byte at offset 0 of entry)
                if (currentData[entryOffset]) {
                    // Extract error information from this entry
                    updateState = static_cast<unsigned char>(currentData[entryOffset + 12]); // Update state
                    buyerValue = static_cast<unsigned char>(currentData[entryOffset + 1]);   // Buyer info
                    procUpdate = updateState;                                                 // Process update
                    sellerValue = *reinterpret_cast<DWORD*>(currentData + entryOffset + 4);  // Registration serial
                    
                    // Log the database error for this entry
                    CUnmannedTraderController::Log(
                        currentController,
                        "\t\t(%d)Nth Regist Serial(%u) dwBuyer(%u) UpdateState(%u) byProcUpdate(%u) DB Error!\r\n",
                        entryIndex,
                        sellerValue,
                        *reinterpret_cast<DWORD*>(currentData + entryOffset + 8), // Buyer ID
                        buyerValue,
                        procUpdate
                    );
                }
            }
        }
    }
}

/**
 * @brief Check if unmanned trader user is in login state
 * @param userInfo Pointer to the unmanned trader user info instance
 * @return bool true if user is in login state (state == 1), false otherwise
 * @note Original Function: ?IsLogInState@CUnmannedTraderUserInfo@@QEAA_NXZ
 * @note Original Address: 0x140366F20
 */
bool CUnmannedTraderUserInfo::IsLogInState(CUnmannedTraderUserInfo* userInfo) {
    // Initialize debug pattern (original decompiled logic)
    int* v1 = reinterpret_cast<int*>(&userInfo);
    for (signed __int64 i = 4; i > 0; --i) {
        *v1 = -858993460;  // Debug pattern 0xCCCCCCCC
        ++v1;
    }

    CUnmannedTraderUserInfo* v5 = userInfo;

    // Check if state equals 1 (logged in state)
    return v5->m_eState == 1;
}
