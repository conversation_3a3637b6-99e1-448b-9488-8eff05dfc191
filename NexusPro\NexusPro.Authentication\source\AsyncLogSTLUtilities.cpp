/*
 * NexusPro Authentication Module
 * AsyncLog STL Utilities Implementation
 * 
 * Original Functions: Multiple STL utility functions for AsyncLogInfo
 * Original Addresses: 0x1403C7DB0 - 0x1403C8C60
 * 
 * Purpose: STL utility functions for AsyncLogInfo construction, destruction, and manipulation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple STL utility functions into single file
 * - Fixed malformed template syntax
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/AsyncLogSTLAllocators.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Constructs pair object in-place
 * @param _Ptr Pointer to location to construct at
 * @param _Val Value to copy construct from
 * 
 * Original Function: ??$_Construct@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@U12@@std@@YAXPEAU?$pair@$$CBHPEAVCAsyncLogInfo@@@0@AEBU10@@Z
 * Original Address: 0x1403C7DB0
 * 
 * Constructs a pair object at the specified location using copy construction.
 */
void __fastcall std::_Construct<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(
    std::pair<int const,CAsyncLogInfo *> *_Ptr, 
    std::pair<int const,CAsyncLogInfo *> *_Val)
{
    __int64 *v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-68h]@1
    void *_Where; // [sp+20h] [bp-48h]@4
    char *v6; // [sp+28h] [bp-40h]@4
    char v7; // [sp+30h] [bp-38h]@5
    std::pair<int const,CAsyncLogInfo *> *v8; // [sp+70h] [bp+8h]@1
    std::pair<int const,CAsyncLogInfo *> *v9; // [sp+78h] [bp+10h]@1

    v9 = _Val;
    v8 = _Ptr;
    v2 = &v4;
    
    // Initialize debug pattern in local variables
    for (i = 22i64; i; --i) {
        *(_DWORD *)v2 = -858993460;
        v2 = (__int64 *)((char *)v2 + 4);
    }
    
    _Where = v8;
    
    // Use placement new to construct the pair
    v6 = (char *)operator new(0x10ui64, v8);
    if (v6) {
        // Copy the pair data
        qmemcpy(&v7, v9, 0x10ui64);
        qmemcpy(v6, &v7, 0x10ui64);
    }
}

/**
 * @brief Destroys list node object
 * @param _Ptr Pointer to node to destroy
 * 
 * Original Function: ??$_Destroy@U_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@std@@YAXPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@@@2@@0@@Z
 * Original Address: 0x1403C7F40
 * 
 * Destroys a list node containing an AsyncLogInfo pair.
 */
void __fastcall std::_Destroy<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>(
    std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *_Ptr)
{
    __int64 *v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-38h]@1
    std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *v4; // [sp+40h] [bp+8h]@1

    v4 = _Ptr;
    v1 = &v3;
    
    // Initialize debug pattern in local variables
    for (i = 12i64; i; --i) {
        *(_DWORD *)v1 = -858993460;
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Destroy the pair value in the node
    if (v4) {
        std::pair<int const,CAsyncLogInfo *>::~pair(&v4->_Myval);
    }
}

/**
 * @brief Destroys range of iterators
 * @param _First Iterator to start of range
 * @param _Last Iterator to end of range
 * @param _Cat Iterator category tag
 * 
 * Original Function: ??$_Destroy@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@Uforward_iterator_tag@2@@std@@YAXV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@0@@@2@@0@0Uforward_iterator_tag@0@@Z
 * Original Address: 0x1403C8C60
 * 
 * Destroys a range of elements specified by iterators.
 */
void __fastcall std::_Destroy<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::forward_iterator_tag>(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> _First,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> _Last,
    std::forward_iterator_tag _Cat)
{
    __int64 *v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-48h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> v6; // [sp+20h] [bp-28h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> v7; // [sp+28h] [bp-20h]@4

    v3 = &v5;
    
    // Initialize debug pattern in local variables
    for (i = 16i64; i; --i) {
        *(_DWORD *)v3 = -858993460;
        v3 = (__int64 *)((char *)v3 + 4);
    }
    
    v6 = _First;
    v7 = _Last;
    
    // Iterate through range and destroy each element
    while (v6._Ptr != v7._Ptr) {
        // Destroy the current element
        std::pair<int const,CAsyncLogInfo *>::~pair(&v6._Ptr->_Myval);
        
        // Move to next element
        v6._Ptr = v6._Ptr->_Next;
    }
}

/**
 * @brief Copy constructs pair object
 * @param _Dest Destination pair
 * @param _Src Source pair to copy from
 * 
 * Helper function for copying AsyncLogInfo pairs.
 */
void __fastcall CopyConstructAsyncLogPair(
    std::pair<int const,CAsyncLogInfo *> *_Dest,
    const std::pair<int const,CAsyncLogInfo *> *_Src)
{
    if (_Dest && _Src) {
        _Dest->first = _Src->first;
        _Dest->second = _Src->second;
    }
}

/**
 * @brief Allocates memory for list nodes
 * @param _Count Number of nodes to allocate
 * @param __formal Formal parameter (unused)
 * @return Pointer to allocated node memory
 *
 * Original Function: ??$_Allocate@U_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C7E90
 *
 * Allocates memory for list nodes with overflow checking.
 */
std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *__fastcall
std::_Allocate<std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node>(
    unsigned __int64 _Count,
    std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *__formal)
{
    __int64 *v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-48h]@1
    std::bad_alloc v6; // [sp+20h] [bp-28h]@7
    unsigned __int64 v7; // [sp+50h] [bp+8h]@1

    v7 = _Count;
    v2 = &v5;

    // Initialize debug pattern in local variables
    for (i = 16i64; i; --i) {
        *(_DWORD *)v2 = -858993460;
        v2 = (__int64 *)((char *)v2 + 4);
    }

    if (v7) {
        // Check for overflow: if max_value / count < node_size, overflow would occur
        if (0xFFFFFFFFFFFFFFFFui64 / v7 < 0x20) {
            // Throw bad_alloc exception for overflow
            std::bad_alloc::bad_alloc(&v6, 0i64);
            CxxThrowException_0(&v6, &TI2_AVbad_alloc_std__);
        }
    } else {
        v7 = 0i64;
    }

    // Allocate memory for nodes (32 bytes per node)
    return (std::_List_nod<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Node *)operator new(32 * v7);
}

/**
 * @brief Destroys pair object
 * @param _Ptr Pointer to pair to destroy
 *
 * Helper function for destroying AsyncLogInfo pairs.
 */
void __fastcall DestroyAsyncLogPair(
    std::pair<int const,CAsyncLogInfo *> *_Ptr)
{
    if (_Ptr) {
        // For simple pairs with pointers, no special destruction needed
        // Just clear the values
        _Ptr->first = 0;
        _Ptr->second = nullptr;
    }
}

/**
 * @brief Creates a pair object from two values
 * @param result Pointer to result pair object
 * @param _Val1 First value (ASYNC_LOG_TYPE enum)
 * @param _Val2 Second value (CAsyncLogInfo pointer)
 * @return Pointer to the constructed pair
 *
 * Original Function: ??$make_pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@YA?AU?$pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@0@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@Z
 * Original Address: 0x1403C75D0
 *
 * Creates a std::pair object from an ASYNC_LOG_TYPE enum and CAsyncLogInfo pointer.
 */
std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *__fastcall std::make_pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *>(
    std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *result,
    ASYNC_LOG_TYPE _Val1,
    CAsyncLogInfo *_Val2)
{
    __int64 *v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v6; // [sp+0h] [bp-28h]@1
    std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *v7; // [sp+30h] [bp+8h]@1
    ASYNC_LOG_TYPE _Val1a; // [sp+38h] [bp+10h]@1
    CAsyncLogInfo *_Val2a; // [sp+40h] [bp+18h]@1

    // Store parameters in local variables
    _Val2a = _Val2;
    _Val1a = _Val1;
    v7 = result;

    // Initialize stack buffer with debug pattern
    v3 = &v6;
    for (i = 8; i > 0; --i) {
        *reinterpret_cast<DWORD*>(v3) = 0xCCCCCCCC; // -858993460
        v3 = reinterpret_cast<__int64*>(reinterpret_cast<char*>(v3) + 4);
    }

    // Construct the pair using the pair constructor
    std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *>::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *>(
        v7, &_Val1a, &_Val2a
    );

    return v7;
}

/**
 * @brief Fills a range with a specified value
 * @param _First Iterator to the beginning of the range
 * @param _Last Iterator to the end of the range
 * @param _Val Value to fill the range with
 *
 * Original Function: ??$fill@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V123@@std@@YAXPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@0@0AEBV120@@Z
 * Original Address: 0x1403C7AA0
 *
 * Fills the range [_First, _Last) with copies of the specified value.
 */
void __fastcall std::fill<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *,std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(
    std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_First,
    std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_Last,
    std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_Val)
{
    __int64 *v3; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> *_Firsta; // [sp+30h] [bp+8h]@1

    // Store first iterator
    _Firsta = _First;

    // Initialize stack buffer with debug pattern
    v3 = &v5;
    for (i = 8; i > 0; --i) {
        *reinterpret_cast<DWORD*>(v3) = 0xCCCCCCCC; // -858993460
        v3 = reinterpret_cast<__int64*>(reinterpret_cast<char*>(v3) + 4);
    }

    // Call the internal _Fill function to perform the actual filling
    std::_Fill<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *,std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(
        _Firsta,
        _Last,
        _Val
    );
}

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
