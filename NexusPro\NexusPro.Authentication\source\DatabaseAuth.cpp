/*
 * NexusPro Authentication Module
 * Database Authentication Implementation
 * 
 * Original Functions: Multiple database authentication functions
 * Original Addresses: 0x140482430, 0x1404836A0
 * 
 * Purpose: Handles database authentication procedures for RF Online cash items and user verification
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed variable declarations
 * - Cleaned up SQL statement formatting
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/DatabaseAuth.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Database {

/**
 * @brief RF Cash Item Database authentication procedure call
 * @note Original Function: ?CallProc_RFOnlineAuth@CRFCashItemDatabase@@QEAAHAEAU_param_cash_select@@@Z
 * @note Original Address: 0x140482430
 */
int64_t __fastcall CRFCashItemDatabase_CallProc_RFOnlineAuth(
    CRFCashItemDatabase* this, 
    _param_cash_select* rParam)
{
    // Initialize debug pattern (original decompiled logic)
    uint64_t* v2 = reinterpret_cast<uint64_t*>(&this);
    for (int64_t i = 96; i > 0; --i) {
        *reinterpret_cast<uint32_t*>(v2) = 0xCCCCCCCC;  // Debug pattern
        v2 = reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v2) + 4);
    }
    
    CRFCashItemDatabase* v15 = this;
    _param_cash_select* v16 = rParam;
    
    // Stack canary for security (original decompiled logic)
    uint64_t v14 = reinterpret_cast<uint64_t>(&this) ^ _security_cookie;
    
    // Prepare SQL statement buffer
    char DstBuf[256] = {0};
    
    // Format the stored procedure call
    sprintf_s(
        DstBuf,
        sizeof(DstBuf),
        "declare @out_amount int exec prc_rfonline_auth '%s', @s_amount = @out_amount output select @out_amount",
        rParam->in_szAcc
    );
    
    int64_t result = -1;
    
    // Log the SQL statement if database logging is enabled
    if (v15->m_bSaveDBLog) {
        // Log the authentication attempt
        CRFCashItemDatabase::LogDatabaseOperation(v15, "RFOnlineAuth", DstBuf);
    }
    
    // Prepare SQL execution variables
    void* SQLStmt = nullptr;
    SQLLEN* StrLen_or_IndPtr = nullptr;
    SQLLEN v10 = 0;
    int16_t v11 = 0;
    uint8_t v12 = 0;
    uint8_t v13 = 0;
    
    // Execute the stored procedure
    if (CRFCashItemDatabase::PrepareStatement(v15, DstBuf, &SQLStmt)) {
        // Bind output parameters
        if (CRFCashItemDatabase::BindParameter(v15, SQLStmt, 1, SQL_PARAM_OUTPUT, 
                                              SQL_C_SLONG, SQL_INTEGER, 0, 0, 
                                              &v11, sizeof(v11), &v10)) {
            
            // Execute the statement
            if (CRFCashItemDatabase::ExecuteStatement(v15, SQLStmt)) {
                // Fetch results
                if (CRFCashItemDatabase::FetchResults(v15, SQLStmt)) {
                    // Get the output amount
                    result = static_cast<int64_t>(v11);
                    
                    // Set return parameters
                    rParam->out_nAmount = v11;
                    rParam->out_bResult = (v11 > 0) ? 1 : 0;
                } else {
                    // Fetch failed
                    result = -2;
                }
            } else {
                // Execution failed
                result = -3;
            }
        } else {
            // Parameter binding failed
            result = -4;
        }
        
        // Clean up statement
        CRFCashItemDatabase::CleanupStatement(v15, SQLStmt);
    } else {
        // Statement preparation failed
        result = -5;
    }
    
    // Verify stack canary (security check)
    if (v14 != (reinterpret_cast<uint64_t>(&this) ^ _security_cookie)) {
        // Stack corruption detected - handle security violation
        __security_check_cookie(v14 ^ reinterpret_cast<uint64_t>(&this));
    }
    
    return result;
}

/**
 * @brief Japanese RF Cash Item Database authentication procedure call
 * @note Original Function: ?CallProc_RFOnlineAuth_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_select@@@Z
 * @note Original Address: 0x1404836A0
 */
int64_t __fastcall CRFCashItemDatabase_CallProc_RFOnlineAuth_Jap(
    CRFCashItemDatabase* this, 
    _param_cash_select* rParam)
{
    // Initialize debug pattern (original decompiled logic)
    uint64_t* v2 = reinterpret_cast<uint64_t*>(&this);
    for (int64_t i = 96; i > 0; --i) {
        *reinterpret_cast<uint32_t*>(v2) = 0xCCCCCCCC;  // Debug pattern
        v2 = reinterpret_cast<uint64_t*>(reinterpret_cast<char*>(v2) + 4);
    }
    
    CRFCashItemDatabase* v15 = this;
    _param_cash_select* v16 = rParam;
    
    // Stack canary for security (original decompiled logic)
    uint64_t v14 = reinterpret_cast<uint64_t>(&this) ^ _security_cookie;
    
    // Prepare SQL statement buffer for Japanese version
    char DstBuf[256] = {0};
    
    // Format the Japanese stored procedure call
    sprintf_s(
        DstBuf,
        sizeof(DstBuf),
        "declare @out_amount int exec prc_rfonline_auth_jap '%s', @s_amount = @out_amount output select @out_amount",
        rParam->in_szAcc
    );
    
    int64_t result = -1;
    
    // Log the SQL statement if database logging is enabled
    if (v15->m_bSaveDBLog) {
        // Log the Japanese authentication attempt
        CRFCashItemDatabase::LogDatabaseOperation(v15, "RFOnlineAuth_Jap", DstBuf);
    }
    
    // Prepare SQL execution variables
    void* SQLStmt = nullptr;
    SQLLEN* StrLen_or_IndPtr = nullptr;
    SQLLEN v10 = 0;
    int16_t v11 = 0;
    uint8_t v12 = 0;
    uint8_t v13 = 0;
    
    // Execute the stored procedure (similar to standard version)
    if (CRFCashItemDatabase::PrepareStatement(v15, DstBuf, &SQLStmt)) {
        // Bind output parameters
        if (CRFCashItemDatabase::BindParameter(v15, SQLStmt, 1, SQL_PARAM_OUTPUT, 
                                              SQL_C_SLONG, SQL_INTEGER, 0, 0, 
                                              &v11, sizeof(v11), &v10)) {
            
            // Execute the statement
            if (CRFCashItemDatabase::ExecuteStatement(v15, SQLStmt)) {
                // Fetch results
                if (CRFCashItemDatabase::FetchResults(v15, SQLStmt)) {
                    // Get the output amount
                    result = static_cast<int64_t>(v11);
                    
                    // Set return parameters
                    rParam->out_nAmount = v11;
                    rParam->out_bResult = (v11 > 0) ? 1 : 0;
                } else {
                    // Fetch failed
                    result = -2;
                }
            } else {
                // Execution failed
                result = -3;
            }
        } else {
            // Parameter binding failed
            result = -4;
        }
        
        // Clean up statement
        CRFCashItemDatabase::CleanupStatement(v15, SQLStmt);
    } else {
        // Statement preparation failed
        result = -5;
    }
    
    // Verify stack canary (security check)
    if (v14 != (reinterpret_cast<uint64_t>(&this) ^ _security_cookie)) {
        // Stack corruption detected - handle security violation
        __security_check_cookie(v14 ^ reinterpret_cast<uint64_t>(&this));
    }
    
    return result;
}

} // namespace Database
} // namespace Authentication
} // namespace NexusPro
