/*
 * NexusPro Authentication Module
 * AsyncLog STL Copy Operations Header
 * 
 * Original Functions: Multiple STL copy and move operations for AsyncLogInfo
 * Original Addresses: 0x1403C7970 - 0x1403C8D20
 * 
 * Purpose: Header for STL copy and move operations for AsyncLogInfo container manipulation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

// Forward declarations for STL types
namespace std {
    template<class T> class allocator;
    template<class T1, class T2> struct pair;
    
    template<class T, class Alloc>
    class list {
    public:
        template<int N> class _Iterator;
    };
    
    // STL algorithm functions
    template<class Iter>
    Iter* unchecked_copy(Iter* _First, Iter* _Last, Iter* _Dest, Iter* result);
    
    template<class Iter>
    Iter* unchecked_uninitialized_copy(Iter* _First, Iter* _Last, Iter* _Dest, Iter* result);
    
    template<class Iter>
    Iter* _Copy_backward_opt(Iter* _First, Iter* _Last, Iter* _Dest, Iter* result);
}

// Forward declaration for CAsyncLogInfo
class CAsyncLogInfo;

/**
 * @brief STL Copy operation function declarations for AsyncLogInfo
 * 
 * These functions provide standard STL copy and move operations for
 * manipulating CAsyncLogInfo objects in containers.
 */

// Type aliases for readability
typedef std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> AsyncLogIterator;

/**
 * @brief Unchecked copy operation for iterators
 */
AsyncLogIterator *__fastcall std::unchecked_copy<AsyncLogIterator>(
    AsyncLogIterator *_First,
    AsyncLogIterator *_Last,
    AsyncLogIterator *_Dest,
    AsyncLogIterator *result);

/**
 * @brief Unchecked uninitialized copy operation
 */
AsyncLogIterator *__fastcall std::unchecked_uninitialized_copy<AsyncLogIterator>(
    AsyncLogIterator *_First,
    AsyncLogIterator *_Last,
    AsyncLogIterator *_Dest,
    AsyncLogIterator *result);

/**
 * @brief Copy backward operation for iterators
 */
AsyncLogIterator *__fastcall std::_Copy_backward_opt<AsyncLogIterator>(
    AsyncLogIterator *_First,
    AsyncLogIterator *_Last,
    AsyncLogIterator *_Dest,
    AsyncLogIterator *result);

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
