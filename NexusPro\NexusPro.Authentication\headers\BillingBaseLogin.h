/**
 * @file BillingBaseLogin.h
 * @brief RF Online Base Billing Login Function Declarations
 * @note Original Function: ?Login@CBilling@@UEAAXPEAVCUserDB@@@Z
 * @note Original Address: 0x14028CAC0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
class CBilling;
class CUserDB;

/**
 * @namespace NexusPro::Authentication::Billing
 * @brief Base billing system authentication functions
 */
namespace NexusPro {
namespace Authentication {
namespace Billing {

/**
 * @brief Base billing system login function
 * @param billing Pointer to the billing instance
 * @param userDatabase Pointer to user database containing login credentials
 *
 * This function handles the base billing login process, initializing memory
 * buffers and calling the virtual login function through the billing vtable.
 * 
 * @note Original Address: 0x14028CAC0
 * @note This is the base CBilling class login function, different from CBillingManager
 */
void CBillingLogin(CBilling* billing, CUserDB* userDatabase);

} // namespace Billing
} // namespace Authentication
} // namespace NexusPro
