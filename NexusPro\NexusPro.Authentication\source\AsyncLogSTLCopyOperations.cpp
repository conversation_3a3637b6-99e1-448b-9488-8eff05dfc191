/*
 * NexusPro Authentication Module
 * AsyncLog STL Copy Operations Implementation
 * 
 * Original Functions: Multiple STL copy and move operations for AsyncLogInfo
 * Original Addresses: 0x1403C7970 - 0x1403C8D20
 * 
 * Purpose: STL copy and move operations for AsyncLogInfo container manipulation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple STL copy/move functions into single file
 * - Fixed malformed template syntax
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/AsyncLogSTLAllocators.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief Unchecked copy operation for iterators
 * @param _First Iterator to start of source range
 * @param _Last Iterator to end of source range
 * @param _Dest Iterator to destination
 * @return Iterator to end of copied range
 * 
 * Original Function: ??$unchecked_copy@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C7970
 * 
 * Performs unchecked copy of elements from source range to destination.
 */
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *__fastcall 
std::unchecked_copy<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_First,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Last,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Dest,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *result)
{
    __int64 *v4; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v7; // [sp+0h] [bp-58h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v8; // [sp+20h] [bp-38h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v9; // [sp+28h] [bp-30h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v10; // [sp+30h] [bp-28h]@4

    v4 = &v7;
    
    // Initialize debug pattern in local variables
    for (i = 20i64; i; --i) {
        *(_DWORD *)v4 = -858993460;
        v4 = (__int64 *)((char *)v4 + 4);
    }
    
    v8 = _First;
    v9 = _Last;
    v10 = _Dest;
    
    // Copy elements from source range to destination
    while (v8->_Ptr != v9->_Ptr) {
        // Copy the pair value
        std::pair<int const,CAsyncLogInfo *>::pair(&v10->_Ptr->_Myval, &v8->_Ptr->_Myval);
        
        // Move to next elements
        v8->_Ptr = v8->_Ptr->_Next;
        v10->_Ptr = v10->_Ptr->_Next;
    }
    
    *result = *v10;
    return result;
}

/**
 * @brief Unchecked uninitialized copy operation
 * @param _First Iterator to start of source range
 * @param _Last Iterator to end of source range
 * @param _Dest Iterator to destination
 * @return Iterator to end of copied range
 * 
 * Original Function: ??$unchecked_uninitialized_copy@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C8D20
 * 
 * Performs unchecked uninitialized copy of elements to uninitialized memory.
 */
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *__fastcall 
std::unchecked_uninitialized_copy<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_First,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Last,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Dest,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *result)
{
    __int64 *v4; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v7; // [sp+0h] [bp-58h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v8; // [sp+20h] [bp-38h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v9; // [sp+28h] [bp-30h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v10; // [sp+30h] [bp-28h]@4

    v4 = &v7;
    
    // Initialize debug pattern in local variables
    for (i = 20i64; i; --i) {
        *(_DWORD *)v4 = -858993460;
        v4 = (__int64 *)((char *)v4 + 4);
    }
    
    v8 = _First;
    v9 = _Last;
    v10 = _Dest;
    
    // Copy construct elements into uninitialized memory
    while (v8->_Ptr != v9->_Ptr) {
        // Use placement new to construct in uninitialized memory
        std::_Construct<std::pair<int const,CAsyncLogInfo *>,std::pair<int const,CAsyncLogInfo *>>(
            &v10->_Ptr->_Myval, &v8->_Ptr->_Myval);
        
        // Move to next elements
        v8->_Ptr = v8->_Ptr->_Next;
        v10->_Ptr = v10->_Ptr->_Next;
    }
    
    *result = *v10;
    return result;
}

/**
 * @brief Copy backward operation for iterators
 * @param _First Iterator to start of source range
 * @param _Last Iterator to end of source range
 * @param _Dest Iterator to destination end
 * @return Iterator to start of copied range
 * 
 * Original Function: ??$_Copy_backward_opt@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@...
 * Original Address: 0x1403C8AD0
 * 
 * Performs backward copy operation for overlapping ranges.
 */
std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *__fastcall 
std::_Copy_backward_opt<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>(
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_First,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Last,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *_Dest,
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *result)
{
    __int64 *v4; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v7; // [sp+0h] [bp-58h]@1
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v8; // [sp+20h] [bp-38h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v9; // [sp+28h] [bp-30h]@4
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> *v10; // [sp+30h] [bp-28h]@4

    v4 = &v7;
    
    // Initialize debug pattern in local variables
    for (i = 20i64; i; --i) {
        *(_DWORD *)v4 = -858993460;
        v4 = (__int64 *)((char *)v4 + 4);
    }
    
    v8 = _First;
    v9 = _Last;
    v10 = _Dest;
    
    // Copy elements backward (from end to beginning)
    while (v9->_Ptr != v8->_Ptr) {
        // Move to previous elements
        v9->_Ptr = v9->_Ptr->_Prev;
        v10->_Ptr = v10->_Ptr->_Prev;
        
        // Copy the pair value
        std::pair<int const,CAsyncLogInfo *>::pair(&v10->_Ptr->_Myval, &v9->_Ptr->_Myval);
    }
    
    *result = *v10;
    return result;
}

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
