/*
 * AsyncLogInfo.h
 * 
 * Header for asynchronous logging functionality in RF Online authentication
 * Restored from original decompiled source
 */

#pragma once

#include <cstdint>

// Forward declarations for RF Online types
class CNetCriticalSection;
class CMyTimer;

namespace RFOnline {
namespace Authentication {
namespace Logging {

/*
 * Async Log Type Enumeration
 * Purpose: Defines different types of async logs
 * Original: ASYNC_LOG_TYPE
 */
enum class AsyncLogType : int {
    UNKNOWN = -1,
    SYSTEM = 0,
    ERROR = 1,
    WARNING = 2,
    INFO = 3,
    DEBUG = 4,
    TRACE = 5,
    SECURITY = 6,
    AUTHENTICATION = 7,
    BILLING = 8,
    NETWORK = 9,
    DATABASE = 10,
    PERFORMANCE = 11,
    AUDIT = 12,
    CUSTOM = 13,
    MAX_TYPES = 14
};

/*
 * CAsyncLogInfo Class
 * Manages asynchronous logging operations for RF Online authentication system
 * Original: CAsyncLogInfo from decompiled authentication module
 */
class CAsyncLogInfo
{
public:
    /*
     * Initialize
     * Original: InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c
     * Purpose: Initializes the async log info with specified parameters
     */
    char __fastcall Init(AsyncLogType eType, const char* szDirPath, const char* szTypeName,
                        bool bAddDateFileName, unsigned int dwUpdateFileNameDelay, void* logLoading);

    /*
     * Update Log File Name
     * Original: UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.c
     */
    void __fastcall UpdateLogFileName();

    /*
     * Get Count
     * Original: GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.c
     */
    unsigned long __fastcall GetCount();

    /*
     * Get Directory Path
     * Original: GetDirPathCAsyncLogInfoQEAAPEBDXZ_1403C1630.c
     */
    const char* __fastcall GetDirPath();

    /*
     * Get File Name
     * Original: GetFileNameCAsyncLogInfoQEAAPEBDXZ_1403C16D0.c
     */
    const char* __fastcall GetFileName();

    /*
     * Get Type Name
     * Original: GetTypeNameCAsyncLogInfoQEAAPEBDXZ_1403C1650.c
     */
    const char* __fastcall GetTypeName();

    /*
     * Increase Count
     * Original: IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.c
     */
    void __fastcall IncreaseCount();

    // Constructor - initializes logging structure
    CAsyncLogInfo();

    // Destructor - cleans up logging resources
    ~CAsyncLogInfo();

public:
    // Member variables (preserved from original decompiled structure)
    AsyncLogType m_eType;           // Log type enumeration
    uint32_t m_dwLogCount;          // Current log count
    char* m_szLogDirPath;           // Log directory path
    char* m_szLogFileName;          // Log file name
    char* m_szTypeName;             // Log type name
    CMyTimer* m_pkTimer;            // Timer for log operations
    CNetCriticalSection* m_csLock;  // Critical section for thread safety
};

} // namespace Logging
} // namespace Authentication
} // namespace RFOnline
