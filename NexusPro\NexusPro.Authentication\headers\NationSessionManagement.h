/*
 * NexusPro Authentication Module
 * Nation Session Management Header
 * 
 * Original Functions: Multiple nation setting manager session functions
 * Original Addresses: 0x140229470, 0x140229400, 0x1402294F0
 * 
 * Purpose: Header for nation-specific session verification and connection handling
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Session {

/**
 * @brief Nation setting manager session first verification
 * @param this Pointer to CNationSettingManager instance
 * @param n Session identifier
 * @return int 1 if verification successful, 0 otherwise
 * @note Original Address: 0x140229470
 */
int __fastcall CNationSettingManager_OnCheckSessionFirstVerify(CNationSettingManager* this, int n);

/**
 * @brief Nation setting manager session connection
 * @param this Pointer to CNationSettingManager instance
 * @param sessionId Session identifier for connection
 * @note Original Address: 0x140229400
 */
void __fastcall CNationSettingManager_OnConnectSession(CNationSettingManager* this, int sessionId);

/**
 * @brief Nation setting manager session disconnection
 * @param this Pointer to CNationSettingManager instance
 * @param sessionId Session identifier for disconnection
 * @note Original Address: 0x1402294F0
 */
void __fastcall CNationSettingManager_OnDisconnectSession(CNationSettingManager* this, int sessionId);

} // namespace Session
} // namespace Authentication
} // namespace NexusPro
