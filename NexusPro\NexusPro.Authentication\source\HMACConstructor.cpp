/*
 * NexusPro Authentication Module
 * HMAC Constructor Implementation
 * 
 * Original Function: ??0?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Original Address: 0x140465820
 * 
 * Purpose: Constructor for CryptoPP HMAC-SHA1 message authentication code implementation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed template syntax
 * - Cleaned up variable naming
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/CryptoValidation.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Crypto {

/**
 * @brief Constructor for HMAC-SHA1 Message Authentication Code Implementation
 * @param this Pointer to the object being constructed
 * 
 * Initializes the CryptoPP HMAC-SHA1 implementation with proper virtual table setup
 * and algorithm initialization. This is critical for cryptographic authentication
 * in the RF Online security system.
 */
void __fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::MessageAuthenticationCodeImpl(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>> *this)
{
    __int64 *v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>> *v4; // [sp+30h] [bp+8h]@1

    v4 = this;
    v1 = &v3;
    
    // Initialize debug pattern in local variables
    for (i = 8i64; i; --i) {
        *(_DWORD *)v1 = -858993460;
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Initialize the base algorithm implementation
    CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>,CryptoPP::HMAC<CryptoPP::SHA1>>::AlgorithmImpl(
        (CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>,CryptoPP::HMAC<CryptoPP::SHA1>> *)&v4->vfptr);
    
    // Set up virtual function table for HashTransformation interface
    v4->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::`vftable'{for `CryptoPP::HashTransformation'};
    
    // Set up virtual function table for SimpleKeyingInterface interface
    v4->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
}

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
