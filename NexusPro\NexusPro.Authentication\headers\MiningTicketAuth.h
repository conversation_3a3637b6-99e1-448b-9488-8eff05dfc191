/**
 * @file MiningTicketAuth.h
 * @brief RF Online Mining Ticket Authentication Function Declarations
 * @note Original Functions: AuthLastCriTicket and AuthLastMentalTicket
 * @note Original Addresses: 0x1400D01D0 and 0x1400CFDB0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Mining ticket authentication structures
struct MiningTicket_AuthKeyTicket {
    uint32_t uiData;  // Packed time data
};

// Forward declarations
class MiningTicket;

/**
 * @namespace NexusPro::Authentication::Mining
 * @brief Mining system authentication functions
 */
namespace NexusPro {
namespace Authentication {
namespace Mining {

/**
 * @brief Authenticates the last critical mining ticket
 * @param miningTicket Pointer to the mining ticket instance
 * @param currentYear Current year value
 * @param currentMonth Current month value
 * @param currentDay Current day value
 * @param currentHour Current hour value
 * @param numOfTime Number of time units
 * @return 1 if authentication succeeds (ticket matches current time), 0 otherwise
 *
 * This function verifies if the last critical mining ticket was taken at the
 * specified time by comparing the stored ticket data with the current time parameters.
 * 
 * **Authentication Process**:
 * 1. **Validation**: Checks if stored critical ticket data exists
 * 2. **Ticket Creation**: Creates authentication key ticket with current time
 * 3. **Comparison**: Compares current ticket with stored last critical ticket
 * 4. **Result**: Returns 1 for match (success), 0 for no match or no data
 * 
 * **Time Parameters**:
 * - **Year**: Current year (unsigned 16-bit)
 * - **Month**: Current month (1-12)
 * - **Day**: Current day (1-31)
 * - **Hour**: Current hour (0-23)
 * - **NumOfTime**: Time unit multiplier
 * 
 * **Use Cases**:
 * - Preventing duplicate critical mining within time window
 * - Enforcing cooldown periods for critical mining operations
 * - Validating mining ticket authenticity
 * - Time-based mining restriction enforcement
 * 
 * @note Original Address: 0x1400D01D0
 * @note Critical for mining system integrity
 * @note Prevents exploitation of critical mining mechanics
 */
__int64 AuthenticateLastCriticalTicket(
    MiningTicket* miningTicket,
    unsigned __int16 currentYear,
    char currentMonth,
    char currentDay,
    char currentHour,
    char numOfTime
);

/**
 * @brief Authenticates the last mental mining ticket
 * @param miningTicket Pointer to the mining ticket instance
 * @param currentYear Current year value
 * @param currentMonth Current month value
 * @param currentDay Current day value
 * @param currentHour Current hour value
 * @param numOfTime Number of time units
 * @return 1 if authentication succeeds (ticket matches current time), 0 otherwise
 *
 * This function verifies if the last mental mining ticket was taken at the
 * specified time by comparing the stored ticket data with the current time parameters.
 * 
 * **Authentication Process**:
 * 1. **Validation**: Checks if stored mental ticket data exists
 * 2. **Ticket Creation**: Creates authentication key ticket with current time
 * 3. **Comparison**: Compares current ticket with stored last mental ticket
 * 4. **Result**: Returns 1 for match (success), 0 for no match or no data
 * 
 * **Mental Mining Features**:
 * - Mental power-based mining operations
 * - Concentration and focus requirements
 * - Time-based mental fatigue tracking
 * - Mental energy cooldown management
 * 
 * **Ticket Storage**:
 * - Mental ticket data stored at beginning of MiningTicket structure
 * - Uses direct pointer casting for efficient access
 * - Maintains compatibility with existing ticket system
 * 
 * **Security Considerations**:
 * - Prevents mental mining exploitation
 * - Enforces proper mental energy recovery periods
 * - Validates mental mining session authenticity
 * - Protects against time manipulation attacks
 * 
 * @note Original Address: 0x1400CFDB0
 * @note Essential for mental mining system balance
 * @note Prevents mental energy exploitation
 */
__int64 AuthenticateLastMentalTicket(
    MiningTicket* miningTicket,
    unsigned __int16 currentYear,
    char currentMonth,
    char currentDay,
    char currentHour,
    char numOfTime
);

} // namespace Mining
} // namespace Authentication
} // namespace NexusPro
