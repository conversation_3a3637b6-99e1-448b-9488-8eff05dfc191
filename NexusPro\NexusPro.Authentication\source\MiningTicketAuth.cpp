/**
 * @file MiningTicketAuth.cpp
 * @brief RF Online Mining Ticket Authentication Functions
 * @note Original Function: ?AuthLastCriTicket@MiningTicket@@QEAAHGEEEE@Z
 * @note Original Address: 0x1400D01D0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: AuthLastCriTicketMiningTicketQEAAHGEEEEZ_1400D01D0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

/**
 * @brief Authenticates the last critical mining ticket
 * @param miningTicket Pointer to the mining ticket instance
 * @param currentYear Current year value
 * @param currentMonth Current month value
 * @param currentDay Current day value
 * @param currentHour Current hour value
 * @param numOfTime Number of time units
 * @return 1 if authentication succeeds (ticket matches current time), 0 otherwise
 *
 * This function verifies if the last critical mining ticket was taken at the
 * specified time by comparing the stored ticket data with the current time parameters.
 */
__int64 MiningTicket::AuthLastCriTicket(
    MiningTicket* miningTicket,
    unsigned __int16 currentYear,
    char currentMonth,
    char currentDay,
    char currentHour,
    char numOfTime) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;                    // Original: v6 (rdi register)
    signed __int64 loopCounter;                // Original: i (rcx register)
    __int64 authResult;                        // Original: result (rax register)
    __int64 stackBuffer;                       // Original: v9 ([sp+0h] [bp-58h])
    MiningTicket::_AuthKeyTicket currentTicket; // Original: v10 ([sp+34h] [bp-24h])
    MiningTicket* currentMiningTicket;         // Original: v11 ([sp+60h] [bp+8h])

    // Initialize parameters
    currentMiningTicket = miningTicket;
    
    // Initialize stack buffer with debug pattern
    bufferPointer = &stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC; // -858993460
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Check if there is a stored last critical ticket
    if (currentMiningTicket->m_dwTakeLastCriTicket.uiData) {
        // Create an authentication key ticket with current time parameters
        MiningTicket::_AuthKeyTicket::Set(
            &currentTicket,
            currentYear,
            currentMonth,
            currentDay,
            currentHour,
            numOfTime
        );
        
        // Compare the current ticket with the stored last critical ticket
        // Return 1 if they match (authentication succeeds), 0 if they don't
        authResult = (MiningTicket::_AuthKeyTicket::operator!=(
            &currentTicket,
            &currentMiningTicket->m_dwTakeLastCriTicket
        ) == 0) ? 1 : 0;
    } else {
        // No stored ticket data - authentication fails
        authResult = 0;
    }
    
    return authResult;
}

/**
 * @brief Authenticates the last mental mining ticket
 * @param miningTicket Pointer to the mining ticket instance
 * @param currentYear Current year value
 * @param currentMonth Current month value
 * @param currentDay Current day value
 * @param currentHour Current hour value
 * @param numOfTime Number of time units
 * @return 1 if authentication succeeds (ticket matches current time), 0 otherwise
 *
 * This function verifies if the last mental mining ticket was taken at the
 * specified time by comparing the stored ticket data with the current time parameters.
 *
 * Original Function: ?AuthLastMentalTicket@MiningTicket@@QEAAHGEEEE@Z
 * Original Address: 0x1400CFDB0
 */
__int64 MiningTicket::AuthLastMentalTicket(
    MiningTicket* miningTicket,
    unsigned __int16 currentYear,
    char currentMonth,
    char currentDay,
    char currentHour,
    char numOfTime) {

    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;                    // Original: v6 (rdi register)
    signed __int64 loopCounter;                // Original: i (rcx register)
    __int64 authResult;                        // Original: result (rax register)
    __int64 stackBuffer;                       // Original: v9 ([sp+0h] [bp-58h])
    MiningTicket::_AuthKeyTicket currentTicket; // Original: v10 ([sp+34h] [bp-24h])
    MiningTicket* sourceMiningTicket;          // Original: Src ([sp+60h] [bp+8h])

    // Initialize parameters
    sourceMiningTicket = miningTicket;

    // Initialize stack buffer with debug pattern
    bufferPointer = &stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC; // -858993460
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }

    // Check if there is a stored last mental ticket
    if (sourceMiningTicket->m_dwTakeLastMentalTicket.uiData) {
        // Create an authentication key ticket with current time parameters
        MiningTicket::_AuthKeyTicket::Set(
            &currentTicket,
            currentYear,
            currentMonth,
            currentDay,
            currentHour,
            numOfTime
        );

        // Compare the current ticket with the stored last mental ticket
        // Note: The original code casts the mining ticket pointer to _AuthKeyTicket*
        // This suggests the mental ticket data is stored at the beginning of the structure
        authResult = (MiningTicket::_AuthKeyTicket::operator!=(
            &currentTicket,
            reinterpret_cast<MiningTicket::_AuthKeyTicket*>(sourceMiningTicket)
        ) == 0) ? 1 : 0;
    } else {
        // No stored ticket data - authentication fails
        authResult = 0;
    }

    return authResult;
}

/**
 * @brief Sets the authentication key ticket with time parameters
 * @param authKeyTicket Pointer to the authentication key ticket instance
 * @param year Year value (14 bits, 0-16383)
 * @param month Month value (4 bits, 0-15)
 * @param day Day value (5 bits, 0-31)
 * @param hour Hour value (5 bits, 0-31)
 * @param numOfTime Number of time units (4 bits, 0-15)
 *
 * This function packs time parameters into a single 32-bit integer using bit manipulation.
 * The data is stored in the following bit layout:
 * - Bits 31-18: Year (14 bits)
 * - Bits 17-14: Month (4 bits)
 * - Bits 13-9:  Day (5 bits)
 * - Bits 8-4:   Hour (5 bits)
 * - Bits 3-0:   NumOfTime (4 bits)
 *
 * Original Function: ?Set@_AuthKeyTicket@MiningTicket@@QEAAXGEEEE@Z
 * Original Address: 0x1400A6BA0
 */
void __fastcall MiningTicket_AuthKeyTicket_Set(
    MiningTicket_AuthKeyTicket* authKeyTicket,
    unsigned __int16 year,
    char month,
    char day,
    char hour,
    char numOfTime) {

    // Pack year into bits 31-18 (14 bits, mask 0x3FFF)
    // Clear existing year bits and set new year value
    authKeyTicket->uiData = ((year & 0x3FFF) << 18) | (authKeyTicket->uiData & 0x3FFFF);

    // Pack month into bits 17-14 (4 bits, mask 0xF)
    // Clear existing month bits and set new month value
    authKeyTicket->uiData = ((month & 0xF) << 14) | (authKeyTicket->uiData & 0xFFFC3FFF);

    // Pack day into bits 13-9 (5 bits, mask 0x1F)
    // Clear existing day bits and set new day value
    authKeyTicket->uiData = ((day & 0x1F) << 9) | (authKeyTicket->uiData & 0xFFFFC1FF);

    // Pack hour into bits 8-4 (5 bits, mask 0x1F)
    // Clear existing hour bits and set new hour value
    authKeyTicket->uiData = (16 * (hour & 0x1F)) | (authKeyTicket->uiData & 0xFFFFFE0F);

    // Pack numOfTime into bits 3-0 (4 bits, mask 0xF)
    // Clear existing numOfTime bits and set new numOfTime value
    authKeyTicket->uiData = (numOfTime & 0xF) | (authKeyTicket->uiData & 0xFFFFFFF0);
}

/**
 * @brief Sets the authentication key ticket with raw data
 * @param authKeyTicket Pointer to the authentication key ticket instance
 * @param sourceData Raw 32-bit data to set directly
 *
 * This function directly sets the ticket data from a raw unsigned integer value.
 * This is useful when the time data is already packed or when copying ticket data.
 *
 * Original Function: ?Set@_AuthKeyTicket@MiningTicket@@QEAAXI@Z
 * Original Address: 0x140078ED0
 */
void __fastcall MiningTicket_AuthKeyTicket_SetRaw(
    MiningTicket_AuthKeyTicket* authKeyTicket,
    unsigned int sourceData) {

    // Directly set the ticket data from the source
    authKeyTicket->uiData = sourceData;
}

/**
 * @brief AuthKeyTicket equality operator
 * @note Original Function: ??8_AuthKeyTicket@MiningTicket@@QEBA_NAEBU01@@Z
 * @note Original Address: 0x1400A6CA0
 */
bool __fastcall MiningTicket_AuthKeyTicket_OperatorEqual(
    MiningTicket_AuthKeyTicket* this,
    MiningTicket_AuthKeyTicket* Src)
{
    // Initialize debug pattern (original decompiled logic)
    uint32_t* v2 = reinterpret_cast<uint32_t*>(&this);
    for (int64_t i = 4; i > 0; --i) {
        *v2 = 0xCCCCCCCC;  // Debug pattern
        ++v2;
    }

    MiningTicket_AuthKeyTicket* v6 = this;

    // Compare the data fields for equality
    return v6->uiData == Src->uiData;
}

/**
 * @brief AuthKeyTicket inequality operator
 * @note Original Function: ??9_AuthKeyTicket@MiningTicket@@QEBA_NAEBU01@@Z
 * @note Original Address: 0x1400CFE90
 */
bool __fastcall MiningTicket_AuthKeyTicket_OperatorNotEqual(
    MiningTicket_AuthKeyTicket* this,
    MiningTicket_AuthKeyTicket* Src)
{
    // Initialize debug pattern (original decompiled logic)
    uint32_t* v2 = reinterpret_cast<uint32_t*>(&this);
    for (int64_t i = 4; i > 0; --i) {
        *v2 = 0xCCCCCCCC;  // Debug pattern
        ++v2;
    }

    MiningTicket_AuthKeyTicket* v6 = this;

    // Compare the data fields for inequality
    return v6->uiData != Src->uiData;
}

} // namespace Mining
} // namespace Authentication
} // namespace NexusPro
