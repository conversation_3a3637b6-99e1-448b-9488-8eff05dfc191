/*
 * ApexSendLogin.cpp
 * Original Function: ?size@_apex_send_login@@QEAAHXZ
 * Original Address: 0x140410BF0
 * 
 * Description: Apex anti-cheat system login packet handling implementation.
 * This class manages the size and structure of login packets sent to the Apex anti-cheat system.
 */

#include "../headers/ApexSendLogin.h"

namespace RFOnline {
namespace Authentication {
namespace Apex {

/*
 * Get Login Packet Size
 * Address: 0x140410BF0
 * Purpose: Returns the fixed size of the apex login packet
 * Original: size_apex_send_loginQEAAHXZ_140410BF0.c
 */
signed __int64 ApexSendLogin::size()
{
    // Original implementation: return 13i64;
    // This is a fixed packet size for the Apex anti-cheat login protocol
    return 13LL;
}

/*
 * Constructor
 * Purpose: Initialize apex send login packet structure
 */
ApexSendLogin::ApexSendLogin()
    : m_initialized(false)
{
    // Initialize packet data buffer to zero
    memset(m_packet_data, 0, sizeof(m_packet_data));
    m_initialized = true;
}

/*
 * Destructor
 * Purpose: Clean up apex send login packet resources
 */
ApexSendLogin::~ApexSendLogin()
{
    // Clear sensitive packet data
    if (m_initialized)
    {
        memset(m_packet_data, 0, sizeof(m_packet_data));
        m_initialized = false;
    }
}

} // namespace Apex
} // namespace Authentication
} // namespace RFOnline
