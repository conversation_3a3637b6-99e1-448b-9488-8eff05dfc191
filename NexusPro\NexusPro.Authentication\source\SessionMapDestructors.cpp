/*
 * NexusPro Authentication Module
 * Session Map Destructors Implementation
 * 
 * Original Functions: Multiple dtor$* functions for _afxSessionMap
 * Original Addresses: 0x14057B120 - 0x14057C810
 * 
 * Purpose: Cleanup destructors for AFX session map and cryptographic parameters
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Consolidated multiple destructor functions into single file
 * - Fixed malformed function syntax
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/CryptoValidation.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Session {

/**
 * @brief Session map destructor 0 - ECP parameters cleanup
 * 
 * Original Function: ?dtor$0@?0???__F_afxSessionMap@@YAXXZ@4HA_0
 * Original Address: 0x14057B120
 * 
 * Cleans up ECP (Elliptic Curve Prime) recommended parameters.
 */
void __fastcall SessionMapDestructor0()
{
    // Clean up ECP recommended parameters
    CryptoPP::EcRecommendedParameters<CryptoPP::ECP>::~EcRecommendedParameters(&unk_184A89708);
}

/**
 * @brief Session map destructor 1 - Additional ECP cleanup
 * 
 * Original Function: ?dtor$1@?0???__F_afxSessionMap@@YAXXZ@4HA_1
 * Original Address: 0x14057B160
 * 
 * Additional cleanup for ECP parameters.
 */
void __fastcall SessionMapDestructor1()
{
    // Clean up additional ECP parameters
    CryptoPP::EcRecommendedParameters<CryptoPP::ECP>::~EcRecommendedParameters(&unk_184A89710);
}

/**
 * @brief Session map destructor 2 - ECP parameters cleanup
 * 
 * Original Function: ?dtor$2@?0???__F_afxSessionMap@@YAXXZ@4HA_2
 * Original Address: 0x14057B1A0
 * 
 * Cleanup for ECP parameters variant 2.
 */
void __fastcall SessionMapDestructor2()
{
    // Clean up ECP parameters variant 2
    CryptoPP::EcRecommendedParameters<CryptoPP::ECP>::~EcRecommendedParameters(&unk_184A89718);
}

/**
 * @brief Session map destructor 3 - ECP parameters cleanup
 * 
 * Original Function: ?dtor$3@?0???__F_afxSessionMap@@YAXXZ@4HA_3
 * Original Address: 0x14057B1E0
 * 
 * Cleanup for ECP parameters variant 3.
 */
void __fastcall SessionMapDestructor3()
{
    // Clean up ECP parameters variant 3
    CryptoPP::EcRecommendedParameters<CryptoPP::ECP>::~EcRecommendedParameters(&unk_184A89720);
}

/**
 * @brief Session map destructor 31 - EC2N parameters cleanup
 * 
 * Original Function: ?dtor$31@?0???__F_afxSessionMap@@YAXXZ@4HA_31
 * Original Address: 0x14057C810
 * 
 * Final cleanup for EC2N (Elliptic Curve Binary Field) parameters.
 */
void __fastcall SessionMapDestructor31()
{
    // Clean up EC2N parameters (binary field elliptic curves)
    CryptoPP::EcRecommendedParameters<CryptoPP::EC2N>::~EcRecommendedParameters(&unk_184A89A00);
}

/**
 * @brief AsyncLogInfo Init destructor helper
 *
 * Original Function: _CAsyncLogInfo::Init_::_1_::dtor$0
 * Original Address: 0x1403BD0C0
 *
 * Exception unwinding destructor for AsyncLogInfo initialization.
 * Cleans up allocated memory during exception handling.
 */
void __fastcall CAsyncLogInfoInitDestructor0(__int64 a1, __int64 a2)
{
    // Delete allocated memory at offset 10640
    // This is part of exception unwinding cleanup
    operator delete(*reinterpret_cast<void**>(a2 + 10640));
}

} // namespace Session
} // namespace Authentication
} // namespace NexusPro
