/**
 * @file SendAutoTradeTaxRate.cpp
 * @brief RF Online Auto Trade Tax Rate Notification Function
 * @note Original Function: ?SendMsg_UserLogInNotifyTaxRate@TRC_AutoTrade@@QEAAXH@Z
 * @note Original Address: 0x1402D8540
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: SendMsg_UserLogInNotifyTaxRateTRC_AutoTradeQEAAXHZ_1402D8540.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External global object (defined elsewhere in the codebase)
extern void* unk_1414F2088;  // Network process instance

/**
 * @brief Sends tax rate notification message to user upon login
 * @param this Pointer to TRC_AutoTrade instance
 * @param n User index/ID to send the notification to
 * 
 * This function notifies a user about the current auto trade tax rate when they log in.
 * The tax rate affects the commission charged on auto trade transactions and is
 * important for players to know when setting up their automated trading operations.
 * 
 * The message contains:
 * - Tax rate status (enabled/disabled)
 * - Message type identifiers for proper client handling
 * 
 * Auto trade tax rates can vary based on:
 * - Server configuration
 * - Player level or status
 * - Economic events or policies
 */
void __fastcall TRC_AutoTrade::SendMsg_UserLogInNotifyTaxRate(TRC_AutoTrade* this, int n) {
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;                    // Original: v2 (rdi)
    signed __int64 loopCounter;                // Original: i (rcx)
    __int64 stackBuffer[28];                   // Original: v4 ([sp+0h] [bp-78h])
    char messageData;                          // Original: szMsg ([sp+34h] [bp-44h])
    char messageType;                          // Original: pbyType ([sp+54h] [bp-24h])
    char subMessageType;                       // Original: v7 ([sp+55h] [bp-23h])

    // Initialize stack buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 28LL; loopCounter; --loopCounter) {
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        bufferPointer = reinterpret_cast<__int64*>(reinterpret_cast<char*>(bufferPointer) + 4);
    }
    
    // Prepare message data
    messageData = 1;       // Tax rate status (1 = enabled/active)
    
    // Set message type identifiers
    messageType = 30;      // Main message type for auto trade notifications
    subMessageType = 39;   // Sub-type for tax rate notifications
    
    // Send the tax rate notification message to the client
    // This informs the client about the current auto trade tax rate policy
    CNetProcess::LoadSendMsg(
        unk_1414F2088,     // Network process instance
        n,                 // Target user index
        &messageType,      // Message type buffer
        &messageData,      // Message data buffer (tax rate status)
        1u);               // Message size (1 byte)
}
