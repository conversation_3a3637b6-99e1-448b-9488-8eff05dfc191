#pragma once

// Account Server Login Functions Header
// Handles account server login operations and world registration
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Server {

// Forward declarations
class CMainThread;
class CNationSettingManager;
class CNetProcess;

// World request structure
struct _open_world_request_wrac {
    char szWorldName[34];       // World name
    unsigned int dwGateIP;      // Gate IP address
    char cbHashVerify[32];      // Hash verification data
    char byType;                // Request type
    char byFlag;                // Request flag

    /**
     * Get size of the structure
     * @return Size in bytes
     */
    static unsigned short size();
};

/**
 * Account Server Login Management
 * Handles login operations to account server and world registration
 */
class AccountServerLogin {
public:
    /**
     * Process account server login
     * @param mainThread Pointer to main thread object
     */
    static void ProcessLogin(CMainThread* mainThread);

    /**
     * Prepare world registration request
     * @param mainThread Pointer to main thread object
     * @param request Pointer to world request structure
     * @return True if request prepared successfully, false otherwise
     */
    static bool PrepareWorldRequest(
        CMainThread* mainThread,
        _open_world_request_wrac* request
    );

    /**
     * Get gate IP address from configuration
     * @param ipBuffer Output buffer for IP string
     * @param bufferSize Buffer size
     * @return IP address as unsigned int
     */
    static unsigned int GetGateIPAddress(char* ipBuffer, size_t bufferSize);

    /**
     * Send world registration message
     * @param request Pointer to world request structure
     * @return True if message sent successfully, false otherwise
     */
    static bool SendWorldRegistration(const _open_world_request_wrac* request);

    /**
     * Initialize hash verification data
     * @param hashBuffer Output buffer for hash data
     * @param bufferSize Buffer size
     */
    static void InitializeHashVerification(char* hashBuffer, size_t bufferSize);

    /**
     * Send cash database DSN request
     */
    static void SendCashDBDSNRequest();

    /**
     * Validate world name
     * @param worldName World name to validate
     * @return True if valid, false otherwise
     */
    static bool ValidateWorldName(const char* worldName);

    /**
     * Get IP address from system
     * @return IP address as unsigned int
     */
    static unsigned int GetSystemIPAddress();

    /**
     * Load configuration from WorldInfo.ini
     * @param configKey Configuration key
     * @param defaultValue Default value if key not found
     * @param outputBuffer Output buffer
     * @param bufferSize Buffer size
     * @return True if configuration loaded successfully, false otherwise
     */
    static bool LoadWorldConfiguration(
        const char* configKey,
        const char* defaultValue,
        char* outputBuffer,
        size_t bufferSize
    );
};

} // namespace Server
} // namespace Authentication
} // namespace RFOnline
