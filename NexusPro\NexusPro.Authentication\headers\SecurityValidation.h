/*
 * SecurityValidation.h
 * Original Function: _ValidateImageBase
 * Original Address: 0x1404DE4C0
 * 
 * Description: Security validation functions for image base and executable integrity checks.
 * These functions validate PE image headers and ensure executable integrity.
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Security {

/*
 * Validate Image Base
 * Original: _ValidateImageBase_1404DE4C0.c
 * Purpose: Validates PE image base and header integrity
 * Parameters:
 *   - pImageBase: Pointer to the image base to validate
 * Returns: Non-zero if valid, 0 if invalid
 */
__int64 __fastcall ValidateImageBase(char* pImageBase);

/*
 * PE Header Constants
 * These constants are used for PE header validation
 */
namespace PEConstants {
    const uint16_t DOS_SIGNATURE = 0x5A4D;    // "MZ" - DOS header signature (23117 decimal)
    const uint32_t NT_SIGNATURE = 0x4550;     // "PE" - NT header signature (17744 decimal)
    const uint16_t MACHINE_AMD64 = 0x20B;     // x64 machine type (523 decimal)
}

} // namespace Security
} // namespace Authentication
} // namespace RFOnline
