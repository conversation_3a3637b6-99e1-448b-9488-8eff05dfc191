/**
 * @file CryptoKeyGeneration.h
 * @brief RF Online Cryptographic Key Generation Function Declarations
 * @note Original Function: ?GenerateEphemeralKeyPair@AuthenticatedKeyAgreementDomain@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEAE1@Z
 * @note Original Address: 0x1405F6600
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
namespace CryptoPP {
    class AuthenticatedKeyAgreementDomain;
    struct RandomNumberGenerator;
}

/**
 * @namespace NexusPro::Authentication::Cryptography
 * @brief Cryptographic key generation functions for RF Online authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Generates an ephemeral key pair for authenticated key agreement
 * @param keyAgreementDomain Pointer to the authenticated key agreement domain
 * @param randomGenerator Pointer to random number generator for key generation
 * @param privateKey Buffer to store the generated private key
 * @param publicKey Buffer to store the generated public key
 *
 * This function generates a temporary (ephemeral) key pair used in authenticated
 * key agreement protocols. The key generation process involves:
 * 
 * 1. **Initialization**: Prepares the key agreement domain for generation
 * 2. **Random Generation**: Uses cryptographically secure random number generation
 * 3. **Key Pair Creation**: Generates mathematically related private/public key pair
 * 4. **Buffer Storage**: Stores keys in provided buffers for immediate use
 * 
 * **Ephemeral Key Properties**:
 * - **Temporary**: Keys are used for a single session or transaction
 * - **Forward Secrecy**: Past communications remain secure if long-term keys are compromised
 * - **Perfect Forward Secrecy**: Each session uses unique keys
 * - **Non-Persistent**: Keys are not stored permanently
 * 
 * **Supported Key Agreement Protocols**:
 * - Elliptic Curve Diffie-Hellman (ECDH)
 * - Discrete Logarithm Diffie-Hellman (DH)
 * - Authenticated key exchange variants
 * 
 * **Security Considerations**:
 * - Uses cryptographically secure random number generation
 * - Keys should be securely erased after use
 * - Proper validation of domain parameters is essential
 * - Implementation follows industry-standard cryptographic practices
 * 
 * @note Original Address: 0x1405F6600
 * @note Critical for secure communication in RF Online's authentication system
 * @note Provides forward secrecy for client-server communications
 */
void GenerateEphemeralKeyPair(
    CryptoPP::AuthenticatedKeyAgreementDomain* keyAgreementDomain,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned __int8* privateKey,
    unsigned __int8* publicKey
);

/**
 * @brief Generates a static key pair for authenticated key agreement
 * @param keyAgreementDomain Pointer to the authenticated key agreement domain
 * @param randomGenerator Pointer to random number generator for key generation
 * @param privateKey Buffer to store the generated private key
 * @param publicKey Buffer to store the generated public key
 *
 * This function generates a long-term (static) key pair used in authenticated
 * key agreement protocols. Unlike ephemeral keys, static keys are persistent
 * and used for:
 *
 * **Static Key Characteristics**:
 * - **Persistent**: Keys are stored and reused across multiple sessions
 * - **Identity-Based**: Used for long-term identity verification
 * - **Certificate-Bound**: Often associated with digital certificates
 * - **Authentication**: Primary purpose is entity authentication
 *
 * **Use Cases**:
 * - Server identity verification
 * - Client certificate authentication
 * - Long-term secure communication establishment
 * - Digital signature operations
 *
 * **Security Considerations**:
 * - Requires secure long-term storage
 * - Should be protected with strong access controls
 * - Regular key rotation policies should be implemented
 * - Compromise affects all past and future communications
 *
 * @note Original Address: 0x1405F65A0
 * @note Used for long-term identity in RF Online's authentication system
 */
void GenerateStaticKeyPair(
    CryptoPP::AuthenticatedKeyAgreementDomain* keyAgreementDomain,
    struct CryptoPP::RandomNumberGenerator* randomGenerator,
    unsigned __int8* privateKey,
    unsigned __int8* publicKey
);

} // namespace Cryptography
} // namespace Authentication
} // namespace NexusPro
