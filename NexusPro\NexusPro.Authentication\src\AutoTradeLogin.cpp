#include "../headers/AutoTradeLogin.h"

// External global variables from original decompiled code
extern char sBuf[0x2800];      // Global string buffer
extern char sData[0x4E20];     // Global data buffer
extern void* unk_1799C6AA0;    // Unknown record data pointer

// External function declarations
extern char* DisplayItemUpgInfo(unsigned char tableCode, unsigned int level);
extern tm* localtime_5(const __int64* time);

namespace RFOnline {
namespace Authentication {
namespace AutoTrade {

void AutoTradeLogin::ProcessLoginSell(
    CMgrAvatorItemHistory* itemHistory,
    const char* buyerName,
    unsigned int buyerSerial,
    const char* buyerID,
    unsigned int registSerial,
    _STORAGE_LIST::_db_con* item,
    __int64 resultTime,
    unsigned int price,
    unsigned int tax,
    unsigned int leftDalant,
    unsigned int leftGold,
    char* fileName)
{
    if (!itemHistory || !buyerName || !buyerID || !item) {
        return;
    }

    // Initialize debug buffer with pattern
    __int64 debugBuffer[44];
    for (int i = 0; i < 44; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    // Convert timestamp to local time
    tm* transactionTime = localtime_5(&resultTime);

    // Clear the string buffer
    sBuf[0] = 0;

    if (transactionTime) {
        // Format transaction log with valid timestamp
        sprintf_s(
            sBuf,
            0x2800,
            "AUTO TRADE(SELL): login sell selldate(%04d-%02d-%02d %02d:%02d:%02d) "
            "reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
            transactionTime->tm_year + 1900,
            transactionTime->tm_mon + 1,
            transactionTime->tm_mday,
            transactionTime->tm_hour,
            transactionTime->tm_min,
            transactionTime->tm_sec,
            registSerial,
            buyerName,
            buyerSerial,
            buyerID,
            price,
            tax,
            leftDalant,
            leftGold,
            itemHistory->m_szCurDate,
            itemHistory->m_szCurTime
        );
    }
    else {
        // Format transaction log with invalid timestamp
        sprintf_s(
            sBuf,
            0x2800,
            "AUTO TRADE(SELL): login sell selldate(invalid) "
            "reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
            registSerial,
            buyerName,
            buyerSerial,
            buyerID,
            price,
            tax,
            leftDalant,
            leftGold,
            itemHistory->m_szCurDate,
            itemHistory->m_szCurTime
        );
    }

    // Append transaction data to global log
    strcat_s(sData, 0x4E20, sBuf);

    // Get item record data
    _base_fld* itemRecord = CRecordData::GetRecord(
        (CRecordData*)((char*)unk_1799C6AA0 + item->m_byTableCode),
        item->m_wItemIndex
    );

    // Get item upgrade information
    char* upgradeInfo = DisplayItemUpgInfo(item->m_byTableCode, item->m_dwLv);

    // Format item information
    sprintf_s(
        sBuf,
        0x2800,
        "\t- %s_%u_@%s[%I64u]\r\n",
        itemRecord->m_strCode,
        item->m_dwDur,
        upgradeInfo,
        item->m_lnUID
    );

    // Append item information to global log
    strcat_s(sData, 0x4E20, sBuf);
}

void AutoTradeLogin::ProcessLoginCancel(
    CMgrAvatorItemHistory* itemHistory,
    unsigned int registSerial)
{
    if (!itemHistory) {
        return;
    }

    // Format cancel log entry
    sprintf_s(
        sBuf,
        0x2800,
        "AUTO TRADE(CANCEL): login cancel reg(%u) [%s %s]\r\n",
        registSerial,
        itemHistory->m_szCurDate,
        itemHistory->m_szCurTime
    );

    // Append to global log
    strcat_s(sData, 0x4E20, sBuf);
}

void AutoTradeLogin::FormatTransactionLog(
    const char* buyerName,
    unsigned int buyerSerial,
    const char* buyerID,
    unsigned int registSerial,
    unsigned int price,
    unsigned int tax,
    unsigned int leftDalant,
    unsigned int leftGold,
    const tm* transactionTime,
    const char* currentDate,
    const char* currentTime,
    char* buffer,
    size_t bufferSize)
{
    if (!buffer || !buyerName || !buyerID) {
        return;
    }

    if (transactionTime) {
        sprintf_s(
            buffer,
            bufferSize,
            "AUTO TRADE(SELL): login sell selldate(%04d-%02d-%02d %02d:%02d:%02d) "
            "reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
            transactionTime->tm_year + 1900,
            transactionTime->tm_mon + 1,
            transactionTime->tm_mday,
            transactionTime->tm_hour,
            transactionTime->tm_min,
            transactionTime->tm_sec,
            registSerial,
            buyerName,
            buyerSerial,
            buyerID,
            price,
            tax,
            leftDalant,
            leftGold,
            currentDate ? currentDate : "",
            currentTime ? currentTime : ""
        );
    }
    else {
        sprintf_s(
            buffer,
            bufferSize,
            "AUTO TRADE(SELL): login sell selldate(invalid) "
            "reg(%u) buyer(%s:%u id:%s) recv(D:%u) tax(%u) $D:%u $G:%u [%s %s]\r\n",
            registSerial,
            buyerName,
            buyerSerial,
            buyerID,
            price,
            tax,
            leftDalant,
            leftGold,
            currentDate ? currentDate : "",
            currentTime ? currentTime : ""
        );
    }
}

void AutoTradeLogin::FormatItemInfo(
    _STORAGE_LIST::_db_con* item,
    char* buffer,
    size_t bufferSize)
{
    if (!item || !buffer) {
        return;
    }

    _base_fld* itemRecord = GetItemRecord(item->m_byTableCode, item->m_wItemIndex);
    char* upgradeInfo = DisplayItemUpgradeInfo(item->m_byTableCode, item->m_dwLv);

    sprintf_s(
        buffer,
        bufferSize,
        "\t- %s_%u_@%s[%I64u]\r\n",
        itemRecord ? itemRecord->m_strCode : "UNKNOWN",
        item->m_dwDur,
        upgradeInfo ? upgradeInfo : "",
        item->m_lnUID
    );
}

_base_fld* AutoTradeLogin::GetItemRecord(
    unsigned char tableCode,
    unsigned short itemIndex)
{
    return CRecordData::GetRecord(
        (CRecordData*)((char*)unk_1799C6AA0 + tableCode),
        itemIndex
    );
}

char* AutoTradeLogin::DisplayItemUpgradeInfo(
    unsigned char tableCode,
    unsigned int level)
{
    return DisplayItemUpgInfo(tableCode, level);
}

void AutoTradeLogin::AppendTransactionData(const char* transactionData)
{
    if (transactionData) {
        strcat_s(sData, 0x4E20, transactionData);
    }
}

void AutoTradeLogin::InitializeLogging()
{
    // Initialize global buffers
    sBuf[0] = 0;
    sData[0] = 0;
}

void AutoTradeLogin::CleanupLogging()
{
    // Clear global buffers
    sBuf[0] = 0;
    sData[0] = 0;
}

} // namespace AutoTrade
} // namespace Authentication
} // namespace RFOnline
