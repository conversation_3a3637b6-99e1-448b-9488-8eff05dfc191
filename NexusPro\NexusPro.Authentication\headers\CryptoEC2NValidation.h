/*
 * NexusPro Authentication Module
 * Cryptographic EC2N Validation Header
 * 
 * Original Functions: Multiple EC2N validation functions
 * Original Addresses: 0x1405ADAD0, 0x1405ADAF0, 0x1405ADD90
 * 
 * Purpose: Header for Elliptic Curve Binary Field (EC2N) parameter validation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/CryptoPPTypes.h"

namespace NexusPro {
namespace Authentication {
namespace Crypto {

/**
 * @brief EC2N DL_GroupParameters validation thunk
 * @param a1 Adjusted this pointer for virtual function call
 * @return int Validation result
 * @note Original Address: 0x1405ADAD0
 */
int __fastcall EC2N_DL_GroupParameters_ValidateThunk(uint64_t a1);

/**
 * @brief EC2N DL_GroupParameters validation implementation
 * @param a1 This pointer to DL_GroupParameters instance
 * @param a2 Random number generator reference
 * @param a3 Validation level
 * @return char 1 if validation successful, 0 otherwise
 * @note Original Address: 0x1405ADAF0
 */
char __fastcall EC2N_DL_GroupParameters_Validate(uint64_t a1, uint64_t a2, uint32_t a3);

/**
 * @brief EC2N DL_GroupParameters validation with this pointer adjustment
 * @param a1 This pointer requiring adjustment
 * @param a2 Random number generator reference
 * @param a3 Validation level
 * @return char 1 if validation successful, 0 otherwise
 * @note Original Address: 0x1405ADD90
 */
char __fastcall EC2N_DL_GroupParameters_ValidateAdjusted(uint64_t a1, uint64_t a2, uint32_t a3);

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
