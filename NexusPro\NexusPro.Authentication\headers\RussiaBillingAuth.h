/*
 * NexusPro Authentication Module
 * Russia Billing Authentication Header
 * 
 * Original Function: ?CallFunc_RFOnline_Auth@CRusiaBillingMgr@@QEAAHAEAU_param_cash_select@@@Z
 * Original Address: 0x1403213A0
 * 
 * Purpose: Header for Russia-specific billing authentication and cash balance checking
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Billing {

/**
 * @brief Russia billing manager authentication function
 * @param this Pointer to CRusiaBillingMgr instance
 * @param rParam Pointer to cash selection parameters
 * @param a3 Balance amount parameter
 * @return int64_t Authentication result (0 for success, negative for error)
 * @note Original Address: 0x1403213A0
 */
int64_t __fastcall CRusiaBillingMgr_CallFunc_RFOnline_Auth(
    CRusiaBillingMgr* this, 
    _param_cash_select* rParam, 
    double a3);

/**
 * @brief Check account balance using Russia billing system
 * @param szAccount Account identifier string
 * @return double Account balance amount
 */
double __fastcall RFACC_CheckBalance(const char* szAccount);

} // namespace Billing
} // namespace Authentication
} // namespace NexusPro
