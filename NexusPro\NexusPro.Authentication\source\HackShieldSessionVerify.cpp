/**
 * @file HackShieldSessionVerify.cpp
 * @brief RF Online HackShield Session Verification Functions
 * @note Original Function: ?OnCheckSession_FirstVerify@CHackShieldExSystem@@UEAA_NH@Z
 * @note Original Address: 0x140417250
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: OnCheckSession_FirstVerifyCHackShieldExSystemUEAA__140417250.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

/**
 * @brief Performs first verification check for HackShield session
 * @param hackShieldSystem Pointer to the HackShield system instance
 * @param sessionId Session identifier to verify
 * @return true if session verification passes, false otherwise
 *
 * This function performs the initial verification of a HackShield session
 * by retrieving the appropriate parameter object and calling its verification
 * method through the virtual function table.
 */
bool CHackShieldExSystem::OnCheckSession_FirstVerify(
    CHackShieldExSystem* hackShieldSystem, 
    int sessionId) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;                    // Original: v2 (rdi register)
    signed __int64 loopCounter;                // Original: i (rcx register)
    bool verificationResult;                   // Original: result (al register)
    __int64 stackBuffer;                       // Original: v5 ([sp+0h] [bp-38h])
    BASE_HACKSHEILD_PARAM* hackShieldParam;    // Original: v6 ([sp+20h] [bp-18h])
    CHackShieldExSystem* currentSystem;        // Original: v7 ([sp+40h] [bp+8h])
    int currentSessionId;                      // Original: na ([sp+48h] [bp+10h])

    // Initialize parameters
    currentSessionId = sessionId;
    currentSystem = hackShieldSystem;
    
    // Initialize stack buffer with debug pattern
    bufferPointer = &stackBuffer;
    for (loopCounter = 12; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern (0xCCCCCCCC = -858993460)
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position (4 bytes)
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Get HackShield parameter object for the specified session
    hackShieldParam = CHackShieldExSystem::GetParam(currentSystem, sessionId);
    
    if (hackShieldParam) {
        // Call virtual function through vtable for session verification
        verificationResult = reinterpret_cast<int(__fastcall*)(BASE_HACKSHEILD_PARAM*, unsigned int)>(
            hackShieldParam->vfptr->OnCheckSession_FirstVerify
        )(
            hackShieldParam,
            static_cast<unsigned int>(currentSessionId)
        );
    } else {
        // No parameter object found - verification fails
        verificationResult = false;
    }
    
    return verificationResult;
}
