/*
 * KeyGeneration.h
 * Original Functions: 
 * - GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.c
 * - GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.c
 * 
 * Description: Cryptographic key generation functions for authenticated key agreement.
 * These functions handle ephemeral and static key pair generation for secure communication.
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Crypto {

/*
 * Authenticated Key Agreement Domain Class
 * Purpose: Handles key generation for authenticated key agreement protocols
 * Original: CryptoPP::AuthenticatedKeyAgreementDomain
 */
class AuthenticatedKeyAgreementDomain {
public:
    /*
     * Generate Ephemeral Key Pair
     * Original: GenerateEphemeralKeyPairAuthenticatedKeyAgreementD_1405F6600.c
     * Purpose: Generates temporary key pairs for one-time use in key agreement
     * Parameters:
     *   - rng: Random number generator for key generation
     *   - private_key: Buffer to store generated private key
     *   - public_key: Buffer to store generated public key
     */
    void __fastcall GenerateEphemeralKeyPair(
        void* rng,
        unsigned __int8* private_key,
        unsigned __int8* public_key
    );
    
    /*
     * Generate Static Key Pair
     * Original: GenerateStaticKeyPairAuthenticatedKeyAgreementDoma_1405F65A0.c
     * Purpose: Generates long-term key pairs for persistent key agreement
     * Parameters:
     *   - rng: Random number generator for key generation
     *   - private_key: Buffer to store generated private key
     *   - public_key: Buffer to store generated public key
     */
    void __fastcall GenerateStaticKeyPair(
        void* rng,
        unsigned __int8* private_key,
        unsigned __int8* public_key
    );

    /*
     * Constructor
     * Purpose: Initialize the authenticated key agreement domain
     */
    AuthenticatedKeyAgreementDomain();
    
    /*
     * Destructor
     * Purpose: Clean up key agreement domain resources
     */
    virtual ~AuthenticatedKeyAgreementDomain();

private:
    // Virtual function table pointer (for CryptoPP compatibility)
    void** vfptr;
    
    /*
     * Internal Key Generation Helper
     * Purpose: Common key generation logic shared between ephemeral and static generation
     */
    void InternalGenerateKeyPair(
        void* rng,
        unsigned __int8* private_key,
        unsigned __int8* public_key,
        bool is_ephemeral
    );
};

} // namespace Crypto
} // namespace Authentication
} // namespace RFOnline
