/*
 * NexusPro Authentication Module
 * Unmanned Trader User Info Header
 * 
 * Original Function: ?IsLogInState@CUnmannedTraderUserInfo@@QEAA_NXZ
 * Original Address: 0x140366F20
 * 
 * Purpose: Header for unmanned trader user information and login state management
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Trading {

/**
 * @brief Unmanned Trader User Information Class
 * 
 * This class manages information about unmanned trader users, which are
 * automated trading systems that can operate without direct user interaction.
 * It tracks login state and other relevant user information.
 */
class CUnmannedTraderUserInfo {
public:
    // Member variables
    int m_eState;  // Current state of the unmanned trader user
    
    // Additional member variables would be defined here based on the full class structure
    // from the original decompiled code
    
    /**
     * @brief Checks if the unmanned trader user is in logged-in state
     * @param this Pointer to CUnmannedTraderUserInfo instance
     * @return bool Returns true if user is logged in, false otherwise
     * 
     * This function checks the current state of an unmanned trader user to determine
     * if they are currently logged into the system.
     * 
     * State values:
     * - 0: Logged out / Disconnected
     * - 1: Logged in / Active
     * - Other values: Various intermediate states
     */
    bool __fastcall IsLogInState(CUnmannedTraderUserInfo *this);
};

} // namespace Trading
} // namespace Authentication
} // namespace NexusPro
