/**
 * @file D3DDeviceInvalidation.h
 * @brief RF Online Direct3D Device Invalidation Function Declarations
 * @note Original Function: ?D3D_R3InvalidateDevice@@YAJXZ
 * @note Original Address: 0x14050B040
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: D3D_R3InvalidateDeviceYAJXZ_14050B040.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

/**
 * @namespace NexusPro::Authentication::Graphics
 * @brief Graphics system functions related to authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Graphics {

/**
 * @brief Invalidates the Direct3D R3 device and releases associated resources
 * @return 0 on success
 *
 * This function performs a complete invalidation of the Direct3D rendering device,
 * releasing all cached resources, shaders, buffers, and render targets. This is
 * typically called when the graphics device is lost or needs to be reset.
 * 
 * **Invalidation Process**:
 * 1. **Nature System**: Invalidates sky and sun rendering systems
 * 2. **Vertex Shaders**: Releases all compiled vertex shader resources
 * 3. **Blur Effects**: Releases vertex buffers used for blur post-processing
 * 4. **Full-Screen Effects**: Releases full-screen post-processing resources
 * 5. **Render Targets**: Safely releases old render target surfaces
 * 6. **Depth Buffers**: Safely releases old stencil/depth buffer surfaces
 * 
 * **When to Call**:
 * - Device lost scenarios (Alt+Tab, resolution change, etc.)
 * - Graphics device reset operations
 * - Application shutdown or cleanup
 * - Graphics driver updates or changes
 * - Memory pressure situations requiring resource cleanup
 * 
 * **Resource Management**:
 * - Uses proper COM reference counting for D3D objects
 * - Prevents double-release by nullifying pointers
 * - Follows RAII principles for automatic cleanup
 * - Ensures no memory leaks during device transitions
 * 
 * **Performance Impact**:
 * - Expensive operation that should be called sparingly
 * - Causes regeneration of all graphics resources
 * - May result in temporary rendering interruption
 * - Best called during loading screens or transitions
 * 
 * @note Original Address: 0x14050B040
 * @note Critical for proper D3D device lifecycle management
 * @note Ensures clean resource cleanup and prevents memory leaks
 */
__int64 InvalidateD3DDevice();

/**
 * @brief R3 device invalidation wrapper function
 * @return Result from D3D_R3InvalidateDevice (0 on success)
 *
 * This function serves as a wrapper for the D3D R3 device invalidation,
 * providing a simplified interface for R3-specific device invalidation.
 *
 * **Purpose**:
 * - Provides a simplified API for R3 device invalidation
 * - Maintains compatibility with existing R3 rendering code
 * - Wraps the comprehensive D3D invalidation process
 *
 * **Usage**:
 * - Called when R3 rendering context needs to be reset
 * - Used during graphics device transitions
 * - Part of the R3 rendering pipeline cleanup
 *
 * @note Original Address: 0x1404E9FC0
 * @note Wrapper for D3D_R3InvalidateDevice function
 * @note Maintains R3 rendering system compatibility
 */
__int32 InvalidateR3Device();

} // namespace Graphics
} // namespace Authentication
} // namespace NexusPro
