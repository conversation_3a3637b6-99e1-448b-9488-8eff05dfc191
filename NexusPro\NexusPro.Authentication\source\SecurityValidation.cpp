/*
 * SecurityValidation.cpp
 * Original Function: _ValidateImageBase
 * Original Address: 0x1404DE4C0
 * 
 * Description: Security validation functions for image base and executable integrity checks.
 * These functions validate PE image headers and ensure executable integrity.
 */

#include "../headers/SecurityValidation.h"

namespace RFOnline {
namespace Authentication {
namespace Security {

/*
 * Validate Image Base
 * Address: 0x1404DE4C0
 * Purpose: Validates PE image base and header integrity
 * Original: _ValidateImageBase_1404DE4C0.c
 * 
 * This function performs a multi-step validation of a PE (Portable Executable) image:
 * 1. Checks for valid DOS header signature ("MZ")
 * 2. Locates and validates NT header signature ("PE")
 * 3. Verifies the machine type is x64 (AMD64)
 */
__int64 __fastcall ValidateImageBase(char* pImageBase)
{
    __int64 result;                    // Original: result (rax register)
    char* nt_header_ptr;               // Original: v2 ([sp+10h] [bp-18h])

    // Check DOS header signature (first 2 bytes should be "MZ" = 0x5A4D = 23117)
    // Original: if ( *(_WORD *)pImageBase == 23117 )
    if (*reinterpret_cast<uint16_t*>(pImageBase) == PEConstants::DOS_SIGNATURE)
    {
        // Get NT header offset from DOS header (at offset 0x3C, which is 15 * 4 bytes)
        // Original: v2 = &pImageBase[*((_DWORD *)pImageBase + 15)];
        uint32_t nt_header_offset = *reinterpret_cast<uint32_t*>(pImageBase + 0x3C);
        nt_header_ptr = &pImageBase[nt_header_offset];
        
        // Check NT header signature (should be "PE\0\0" = 0x4550 = 17744)
        // Original: if ( *(_DWORD *)v2 == 17744 )
        if (*reinterpret_cast<uint32_t*>(nt_header_ptr) == PEConstants::NT_SIGNATURE)
        {
            // Check machine type in COFF header (at offset 24 bytes from NT signature)
            // Should be IMAGE_FILE_MACHINE_AMD64 = 0x20B = 523
            // Original: result = *((_WORD *)v2 + 12) == 523;
            uint16_t machine_type = *reinterpret_cast<uint16_t*>(nt_header_ptr + 24);
            result = (machine_type == PEConstants::MACHINE_AMD64) ? 1LL : 0LL;
        }
        else
        {
            // Invalid NT header signature
            result = 0LL;
        }
    }
    else
    {
        // Invalid DOS header signature
        result = 0LL;
    }
    
    return result;
}

} // namespace Security
} // namespace Authentication
} // namespace RFOnline
