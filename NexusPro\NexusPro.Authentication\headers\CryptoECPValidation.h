/*
 * NexusPro Authentication Module
 * Cryptographic ECP Validation Header
 * 
 * Original Functions: Multiple ECP validation functions
 * Original Addresses: 0x14046A900, 0x14046A920, 0x14046AD80
 * 
 * Purpose: Header for Elliptic Curve Prime Field (ECP) parameter validation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/CryptoPPTypes.h"

namespace NexusPro {
namespace Authentication {
namespace Crypto {

/**
 * @brief ECP DL_GroupParameters validation thunk
 * @param a1 Adjusted this pointer for virtual function call
 * @param a2 Random number generator reference
 * @param a3 Validation level
 * @return bool true if validation successful, false otherwise
 * @note Original Address: 0x14046A900
 */
bool __fastcall ECP_DL_GroupParameters_ValidateThunk(uint64_t a1, CryptoPP::RandomNumberGenerator* a2, uint32_t a3);

/**
 * @brief ECP DL_GroupParameters validation implementation
 * @param this Pointer to DL_GroupParameters instance
 * @param rng Random number generator reference
 * @param level Validation level
 * @return char 1 if validation successful, 0 otherwise
 * @note Original Address: 0x14046A920
 */
char __fastcall ECP_DL_GroupParameters_Validate(
    CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>* this, 
    CryptoPP::RandomNumberGenerator* rng, 
    uint32_t level);

/**
 * @brief ECP DL_GroupParameters validation with this pointer adjustment
 * @param a1 This pointer requiring adjustment
 * @param a2 Random number generator reference
 * @param a3 Validation level
 * @return char 1 if validation successful, 0 otherwise
 * @note Original Address: 0x14046AD80
 */
char __fastcall ECP_DL_GroupParameters_ValidateAdjusted(uint64_t a1, CryptoPP::RandomNumberGenerator* a2, uint32_t a3);

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
