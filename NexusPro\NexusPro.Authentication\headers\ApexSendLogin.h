/*
 * ApexSendLogin.h
 * Original Function: ?size@_apex_send_login@@QEAAHXZ
 * Original Address: 0x140410BF0
 * 
 * Description: Apex anti-cheat system login packet handling.
 * This class manages the size and structure of login packets sent to the Apex anti-cheat system.
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Apex {

/*
 * Apex Send Login Class
 * Purpose: Handles login packet structure and size for Apex anti-cheat system
 * Original: _apex_send_login class
 */
class ApexSendLogin {
public:
    /*
     * Get Login Packet Size
     * Original: size_apex_send_loginQEAAHXZ_140410BF0.c
     * Purpose: Returns the fixed size of the apex login packet
     * Returns: Always returns 13 bytes (fixed packet size)
     */
    signed __int64 size();
    
    /*
     * Constructor
     * Purpose: Initialize apex send login packet structure
     */
    ApexSendLogin();
    
    /*
     * Destructor
     * Purpose: Clean up apex send login packet resources
     */
    ~ApexSendLogin();

private:
    // Private members would be defined based on the actual packet structure
    // The size() function indicates this is a 13-byte packet
    char m_packet_data[13];  // Fixed size packet buffer
    bool m_initialized;      // Initialization state
};

} // namespace Apex
} // namespace Authentication
} // namespace RFOnline
