/*
 * NexusPro Authentication Module
 * AsyncLog STL Constructors Header
 * 
 * Original Functions: Multiple STL constructor functions for AsyncLogInfo containers
 * Original Addresses: 0x1403C7E70, 0x1403C1560, 0x1403C1480, etc.
 * 
 * Purpose: Header for STL container constructors for AsyncLogInfo pair and iterator types
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/STLTypes.h"
#include "AsyncLogInfo.h"

namespace NexusPro {
namespace Authentication {
namespace STL {

/**
 * @brief STL allocator constructor for list node pointers
 * @param this Pointer to allocator instance
 * @param formal Formal parameter for allocator construction
 * @note Original Address: 0x1403C7E70
 */
void __fastcall AsyncLogListNodeAllocatorConstructor(
    std::allocator<std::_List_nod<std::pair<int const, CAsyncLogInfo*>, 
                                  std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Node*>* this,
    const std::allocator<std::pair<int const, CAsyncLogInfo*>>* formal);

/**
 * @brief STL bidirectional iterator copy constructor
 * @param this Pointer to iterator instance
 * @param that Source iterator to copy from
 * @note Original Address: 0x1403C1560
 */
void __fastcall AsyncLogBidirectionalIteratorConstructor(
    std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, 
                std::pair<int const, CAsyncLogInfo*> const*, 
                std::pair<int const, CAsyncLogInfo*> const&>* this,
    const std::_Bidit<std::pair<int const, CAsyncLogInfo*>, __int64, 
                      std::pair<int const, CAsyncLogInfo*> const*, 
                      std::pair<int const, CAsyncLogInfo*> const&>* that);

/**
 * @brief STL const iterator constructor for list
 * @param this Pointer to const iterator instance
 * @param that Source const iterator to copy from
 * @note Original Address: 0x1403C1480
 */
void __fastcall AsyncLogListConstIteratorConstructor(
    std::_Const_iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                                     std::allocator<std::pair<int const, CAsyncLogInfo*>>>>* this,
    const std::_Const_iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                                           std::allocator<std::pair<int const, CAsyncLogInfo*>>>>* that);

/**
 * @brief STL iterator default constructor for list with allocator
 * @param this Pointer to iterator instance
 * @note Original Address: 0x1403C42C0
 */
void __fastcall AsyncLogListIteratorDefaultConstructor(
    std::_Iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                               std::allocator<std::pair<int const, CAsyncLogInfo*>>>>* this);

/**
 * @brief STL vector iterator copy constructor
 * @param this Pointer to vector iterator instance
 * @param that Source vector iterator to copy from
 * @note Original Address: 0x1403C5DC0
 */
void __fastcall AsyncLogVectorIteratorCopyConstructor(
    std::_Vector_iterator<std::_Iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                                                     std::allocator<std::pair<int const, CAsyncLogInfo*>>>>, 
                         std::allocator<std::_Iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                                                                   std::allocator<std::pair<int const, CAsyncLogInfo*>>>>>>* this,
    const std::_Vector_iterator<std::_Iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                                                           std::allocator<std::pair<int const, CAsyncLogInfo*>>>>, 
                               std::allocator<std::_Iterator<0, std::list<std::pair<int const, CAsyncLogInfo*>, 
                                                                         std::allocator<std::pair<int const, CAsyncLogInfo*>>>>>>* that);

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
