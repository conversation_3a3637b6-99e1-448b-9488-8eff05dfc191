<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{49872A69-4E44-481A-831F-CBDC9AEF1F1B}</ProjectGuid>
    <RootNamespace>NexusProAuthentication</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.Authentication</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <AdditionalIncludeDirectories>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <AdditionalIncludeDirectories>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>

  <!-- Source Files -->
  <ItemGroup>
    <ClCompile Include="source\AccountServerLogin.cpp" />
    <ClCompile Include="source\ApexSendLoginSize.cpp" />
    <ClCompile Include="source\AsyncLogInfo.cpp" />
    <ClCompile Include="source\AsyncLogHashMapConstructor.cpp" />
    <ClCompile Include="source\AsyncLogListConstructor.cpp" />
    <ClCompile Include="source\AsyncLogSTLAllocators.cpp" />
    <ClCompile Include="source\AsyncLogSTLHashFunctions.cpp" />
    <ClCompile Include="source\AsyncLogSTLListOperations.cpp" />
    <ClCompile Include="source\AsyncLogSTLCopyOperations.cpp" />
    <ClCompile Include="source\AsyncLogSTLUtilities.cpp" />
    <ClCompile Include="source\AuthCriTicket.cpp" />
    <ClCompile Include="source\AuthKeyTicketEquals.cpp" />
    <ClCompile Include="source\AuthKeyTicketNotEquals.cpp" />
    <ClCompile Include="source\AuthMentalTicket.cpp" />
    <ClCompile Include="source\AuthMiningTicket.cpp" />
    <ClCompile Include="source\AutoTradeLoginSell.cpp" />
    <ClCompile Include="source\AutoTradeTaxRateNotify.cpp" />
    <ClCompile Include="source\AutoTradeTaxRateNotifyLogin.cpp" />
    <ClCompile Include="source\BillingLogin.cpp" />
    <ClCompile Include="source\BillingBaseLogin.cpp" />
    <ClCompile Include="source\CashItemDatabaseAuth.cpp" />
    <ClCompile Include="source\CompleteLoginCompete.cpp" />
    <ClCompile Include="source\CryptoValidation.cpp" />
    <ClCompile Include="source\DSAValidation.cpp" />
    <ClCompile Include="source\DialogOccFunctions.cpp" />
    <ClCompile Include="source\ECPPrivateKeyValidate.cpp" />
    <ClCompile Include="source\EnglandBillingAuth.cpp" />
    <ClCompile Include="source\GenerateEphemeralKeyPair.cpp" />
    <ClCompile Include="source\GuildBattleGuildLogin.cpp" />
    <ClCompile Include="source\GuildBattleManagerLogin.cpp" />
    <ClCompile Include="source\GuildBattleMemberLogin.cpp" />
    <ClCompile Include="source\HackShieldClientCheckSumResponse.cpp" />
    <ClCompile Include="source\HackShieldClientCrcResponse.cpp" />
    <ClCompile Include="source\HackShieldRecvSession.cpp" />
    <ClCompile Include="source\HackShieldServerCheckSumRequest.cpp" />
    <ClCompile Include="source\HackShieldSessionFirstVerify.cpp" />
    <ClCompile Include="source\HMACConstructor.cpp" />
    <ClCompile Include="source\HMACDestructor.cpp" />
    <ClCompile Include="source\InitAuthKeyTicket.cpp" />
    <ClCompile Include="source\InvalidationFunctions.cpp" />
    <ClCompile Include="source\IsLoginState.cpp" />
    <ClCompile Include="source\JapanBillingLogin.cpp" />
    <ClCompile Include="source\JapanCashItemDatabaseAuth.cpp" />
    <ClCompile Include="source\JumpFunctions.cpp" />
    <ClCompile Include="source\LoginCancelAutoTrade.cpp" />
    <ClCompile Include="source\LoginControlServer.cpp" />
    <ClCompile Include="source\LoginWebAgentServer.cpp" />
    <ClCompile Include="source\LuaTableValidation.cpp" />
    <ClCompile Include="source\NationConnectSession.cpp" />
    <ClCompile Include="source\NationDisconnectSession.cpp" />
    <ClCompile Include="source\NationSessionFirstVerify.cpp" />
    <ClCompile Include="source\NormalGuildBattleLogin.cpp" />
    <ClCompile Include="source\NotifyRaceBuffLogin.cpp" />
    <ClCompile Include="source\NullBillingLogin.cpp" />
    <ClCompile Include="source\OnCheckSessionFirstVerify.cpp" />
    <ClCompile Include="source\OnConnectSession.cpp" />
    <ClCompile Include="source\OnDisconnectSession.cpp" />
    <ClCompile Include="source\OnLoopSession.cpp" />
    <ClCompile Include="source\RusiaBillingAuth.cpp" />
    <ClCompile Include="source\SessionMapDestructors.cpp" />
    <ClCompile Include="source\SendAllUserLogin.cpp" />
    <ClCompile Include="source\SendBillingIDLogin.cpp" />
    <ClCompile Include="source\SendGuildMemberLogin.cpp" />
    <ClCompile Include="source\SendLoginMessage.cpp" />
    <ClCompile Include="source\SetAuthKeyTicket.cpp" />
    <ClCompile Include="source\SetMiningTicketAuth.cpp" />
    <ClCompile Include="source\SetMiningTicketAuthData.cpp" />
    <ClCompile Include="source\SetMiningTicketAuthDataDirect.cpp" />
    <ClCompile Include="source\SetMiningTicketAuthKey.cpp" />
    <ClCompile Include="source\UpdateLoginComplete.cpp" />
    <ClCompile Include="source\UnmannedTraderUserInfo.cpp" />
    <ClCompile Include="source\UpdateTrunkPassword.cpp" />
    <ClCompile Include="source\ValidateGFPPrivateKey.cpp" />
    <ClCompile Include="source\ValidateGFPSafePrimePrivateKey.cpp" />
    <ClCompile Include="source\ValidateEC2NElement.cpp" />
    <ClCompile Include="source\ValidateEC2NGroupValidation.cpp" />
    <ClCompile Include="source\ValidateImageBase.cpp" />
    <ClCompile Include="source\BillingIDLogin.cpp" />
    <ClCompile Include="source\ValidateECPParameters.cpp" />
    <ClCompile Include="source\ValidateEC2NParameters.cpp" />
    <ClCompile Include="source\BillingJPLogin.cpp" />
    <ClCompile Include="source\BillingNULLLogin.cpp" />
    <ClCompile Include="source\ValidateDLGroupParameters.cpp" />
    <ClCompile Include="source\InvalidateNature.cpp" />
    <ClCompile Include="source\NotifyLoginSetBuff.cpp" />
    <ClCompile Include="source\SendAutoTradeTaxRate.cpp" />
    <ClCompile Include="source\ValidateIntegerBasedGroup.cpp" />
    <ClCompile Include="source\ValidateECPGroup.cpp" />
    <ClCompile Include="source\ValidateIntegerElement.cpp" />
    <ClCompile Include="source\HolyStoneAuth.cpp" />
    <ClCompile Include="source\HackShieldSessionVerify.cpp" />
    <ClCompile Include="source\CryptoKeyGeneration.cpp" />
    <ClCompile Include="source\ApexLoginFunctions.cpp" />
    <ClCompile Include="source\NatureInvalidation.cpp" />
    <ClCompile Include="source\D3DDeviceInvalidation.cpp" />
    <ClCompile Include="source\FontDeviceInvalidation.cpp" />
    <ClCompile Include="source\UnmannedTraderLoginComplete.cpp" />
    <ClCompile Include="source\MiningTicketAuth.cpp" />
    <ClCompile Include="source\test_build.cpp" />
    <ClCompile Include="source\CryptoHMACConstructor.cpp" />
    <ClCompile Include="source\AsyncLogSTLConstructors.cpp" />
    <ClCompile Include="source\CryptoEC2NValidation.cpp" />
    <ClCompile Include="source\NationSessionManagement.cpp" />
    <ClCompile Include="source\AsyncLogHashMapOperations.cpp" />
    <ClCompile Include="source\DatabaseAuth.cpp" />
    <ClCompile Include="source\RussiaBillingAuth.cpp" />
    <ClCompile Include="source\CryptoECPValidation.cpp" />
    <ClCompile Include="source\RenderingInvalidation.cpp" />
    <ClCompile Include="source\CryptoIntegerValidation.cpp" />
    <ClCompile Include="source\ApexLoginSize.cpp" />
    <ClCompile Include="source\BillingRegionalLogin.cpp" />
    <ClCompile Include="source\NetworkServerLogin.cpp" />
  </ItemGroup>

  <!-- Header Files -->
  <ItemGroup>
    <ClInclude Include="headers\AccountServerLogin.h" />
    <ClInclude Include="headers\ApexSendLoginSize.h" />
    <ClInclude Include="headers\AsyncLogInfo.h" />
    <ClInclude Include="headers\AsyncLogSTLAllocators.h" />
    <ClInclude Include="headers\AsyncLogSTLHashFunctions.h" />
    <ClInclude Include="headers\AsyncLogSTLListOperations.h" />
    <ClInclude Include="headers\AsyncLogSTLCopyOperations.h" />
    <ClInclude Include="headers\AsyncLogSTLUtilities.h" />
    <ClInclude Include="headers\AuthTickets.h" />
    <ClInclude Include="headers\AutoTradeSystem.h" />
    <ClInclude Include="headers\AutoTradeTaxRateNotifyLogin.h" />
    <ClInclude Include="headers\BillingSystem.h" />
    <ClInclude Include="headers\BillingBaseLogin.h" />
    <ClInclude Include="headers\UnmannedTraderUserInfo.h" />
    <ClInclude Include="headers\CryptoValidation.h" />
    <ClInclude Include="headers\DatabaseSystem.h" />
    <ClInclude Include="headers\DSAValidation.h" />
    <ClInclude Include="headers\DialogOccFunctions.h" />
    <ClInclude Include="headers\GuildBattleSystem.h" />
    <ClInclude Include="headers\HackShieldSystem.h" />
    <ClInclude Include="headers\ValidateEC2NGroupValidation.h" />
    <ClInclude Include="headers\HMACConstructor.h" />
    <ClInclude Include="headers\HMACDestructor.h" />
    <ClInclude Include="headers\InvalidationFunctions.h" />
    <ClInclude Include="headers\JumpFunctions.h" />
    <ClInclude Include="headers\LuaTableValidation.h" />
    <ClInclude Include="headers\MessageSystem.h" />
    <ClInclude Include="headers\ServerConnections.h" />
    <ClInclude Include="headers\SessionMapDestructors.h" />
    <ClInclude Include="headers\SetMiningTicketAuthDataDirect.h" />
    <ClInclude Include="headers\SetMiningTicketAuthKey.h" />
    <ClInclude Include="headers\TicketManagement.h" />
    <ClInclude Include="headers\ValidateGFPPrivateKey.h" />
    <ClInclude Include="headers\ValidateGFPSafePrimePrivateKey.h" />
    <ClInclude Include="headers\ValidateImageBase.h" />
    <ClInclude Include="headers\ValidateECPParameters.h" />
    <ClInclude Include="headers\ValidateEC2NParameters.h" />
    <ClInclude Include="headers\ValidateDLGroupParameters.h" />
    <ClInclude Include="headers\ValidateIntegerBasedGroup.h" />
    <ClInclude Include="headers\ValidateECPGroup.h" />
    <ClInclude Include="headers\ValidateIntegerElement.h" />
    <ClInclude Include="headers\HolyStoneAuth.h" />
    <ClInclude Include="headers\HackShieldSessionVerify.h" />
    <ClInclude Include="headers\CryptoKeyGeneration.h" />
    <ClInclude Include="headers\ApexLoginFunctions.h" />
    <ClInclude Include="headers\NatureInvalidation.h" />
    <ClInclude Include="headers\D3DDeviceInvalidation.h" />
    <ClInclude Include="headers\FontDeviceInvalidation.h" />
    <ClInclude Include="headers\UnmannedTraderLoginComplete.h" />
    <ClInclude Include="headers\MiningTicketAuth.h" />
    <ClInclude Include="headers\CryptoHMACConstructor.h" />
    <ClInclude Include="headers\AsyncLogSTLConstructors.h" />
    <ClInclude Include="headers\CryptoEC2NValidation.h" />
    <ClInclude Include="headers\NationSessionManagement.h" />
    <ClInclude Include="headers\AsyncLogHashMapOperations.h" />
    <ClInclude Include="headers\DatabaseAuth.h" />
    <ClInclude Include="headers\RussiaBillingAuth.h" />
    <ClInclude Include="headers\CryptoECPValidation.h" />
    <ClInclude Include="headers\RenderingInvalidation.h" />
    <ClInclude Include="headers\CryptoIntegerValidation.h" />
    <ClInclude Include="headers\ApexLoginSize.h" />
    <ClInclude Include="headers\BillingRegionalLogin.h" />
    <ClInclude Include="headers\NetworkServerLogin.h" />
  </ItemGroup>

  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
