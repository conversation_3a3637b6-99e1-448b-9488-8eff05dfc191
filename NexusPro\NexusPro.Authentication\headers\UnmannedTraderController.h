#pragma once

// Unmanned Trader Controller Functions Header
// Handles unmanned trader login and update operations
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations for Windows types
typedef struct _SYSTEMTIME SYSTEMTIME;

namespace RFOnline {
namespace Authentication {
namespace Trading {

// Forward declarations
class CRFWorldDatabase;

/**
 * Unmanned Trader Controller
 * Handles unmanned trader login and update operations
 */
class UnmannedTraderController {
private:
    // Database connection
    CRFWorldDatabase* m_pDatabase;

    // Log buffer
    char m_szLogBuffer[1024];

public:
    /**
     * Default constructor
     */
    UnmannedTraderController();

    /**
     * Constructor with database
     * @param database Pointer to world database
     */
    explicit UnmannedTraderController(CRFWorldDatabase* database);

    /**
     * Destructor
     */
    ~UnmannedTraderController();

    /**
     * Initialize controller
     * @param database Pointer to world database
     * @return True if initialization successful, false otherwise
     */
    bool Initialize(CRFWorldDatabase* database);

    /**
     * Complete login competition
     * @param pData Login data
     */
    void CompleteLoginCompete(char* pData);

    /**
     * Update login complete
     * @param pData Login data
     * @return 0 on success, non-zero on error
     */
    char UpdateLoginComplete(char* pData);

    /**
     * Process login
     * @param pData Login data
     * @return True if login successful, false otherwise
     */
    bool ProcessLogin(char* pData);

    /**
     * Process logout
     * @param pData Logout data
     * @return True if logout successful, false otherwise
     */
    bool ProcessLogout(char* pData);

    /**
     * Update trader item state
     * @param traderType Trader type
     * @param registSerial Registration serial
     * @param updateState Update state
     * @param currentTime Current time
     * @return True if update successful, false otherwise
     */
    bool UpdateTraderItemState(
        char traderType,
        unsigned int registSerial,
        char updateState,
        SYSTEMTIME* currentTime
    );

    /**
     * Update trader result info
     * @param traderType Trader type
     * @param registSerial Registration serial
     * @param updateState Update state
     * @param buyerID Buyer ID
     * @param tax Tax amount
     * @param currentTime Current time
     * @return True if update successful, false otherwise
     */
    bool UpdateTraderResultInfo(
        char traderType,
        unsigned int registSerial,
        char updateState,
        unsigned int buyerID,
        unsigned int tax,
        SYSTEMTIME* currentTime
    );

    /**
     * Log message
     * @param format Format string
     * @param ... Additional arguments
     */
    void Log(const char* format, ...);

    /**
     * Get database
     * @return Pointer to world database
     */
    CRFWorldDatabase* GetDatabase() const;

    /**
     * Set database
     * @param database Pointer to world database
     */
    void SetDatabase(CRFWorldDatabase* database);
};

} // namespace Trading
} // namespace Authentication
} // namespace RFOnline
