#pragma once

// User Database Password Functions Header
// Handles user database password operations including trunk password updates
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Database {

// Forward declarations
class CUserDB;

/**
 * User Database Password Management
 * Handles password operations for user database including trunk passwords
 */
class UserDBPassword {
public:
    /**
     * Update trunk password for user
     * @param userDB Pointer to user database object
     * @param newPassword New password string
     * @return True if password updated successfully, false otherwise
     */
    static bool UpdateTrunkPassword(CUserDB* userDB, const char* newPassword);

    /**
     * Validate trunk password
     * @param userDB Pointer to user database object
     * @param password Password to validate
     * @return True if password is valid, false otherwise
     */
    static bool ValidateTrunkPassword(CUserDB* userDB, const char* password);

    /**
     * Initialize trunk password system
     * @param userDB Pointer to user database object
     */
    static void InitializeTrunkPassword(CUserDB* userDB);

    /**
     * Clear trunk password
     * @param userDB Pointer to user database object
     */
    static void ClearTrunkPassword(CUserDB* userDB);

    /**
     * Check if trunk password is set
     * @param userDB Pointer to user database object
     * @return True if password is set, false otherwise
     */
    static bool HasTrunkPassword(CUserDB* userDB);

    /**
     * Get maximum password length
     * @return Maximum allowed password length
     */
    static size_t GetMaxPasswordLength();

    /**
     * Validate password format
     * @param password Password to validate
     * @return True if format is valid, false otherwise
     */
    static bool ValidatePasswordFormat(const char* password);
};

} // namespace Database
} // namespace Authentication
} // namespace RFOnline
