/**
 * @file ApexLoginFunctions.h
 * @brief RF Online Apex Login System Function Declarations
 * @note Original Function: ?size@_apex_send_login@@QEAAHXZ
 * @note Original Address: 0x140410BF0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: size_apex_send_loginQEAAHXZ_140410BF0.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
class _apex_send_login;

/**
 * @namespace NexusPro::Authentication::Apex
 * @brief Apex anti-cheat system authentication functions
 */
namespace NexusPro {
namespace Authentication {
namespace Apex {

/**
 * @brief Returns the size of the apex send login structure
 * @param apexLogin Pointer to the apex send login instance
 * @return Size of the structure in bytes (13 bytes)
 *
 * This function returns the fixed size of the apex send login data structure
 * used for authentication communications with the Apex anti-cheat system.
 * 
 * The apex send login structure contains:
 * - Authentication tokens
 * - Session identifiers
 * - Security validation data
 * - Protocol version information
 * 
 * The fixed size of 13 bytes ensures consistent data transmission
 * and proper protocol compliance with the Apex anti-cheat system.
 * 
 * @note Original Address: 0x140410BF0
 * @note Used for Apex anti-cheat authentication in RF Online
 * @note Fixed size structure for network protocol compatibility
 */
signed __int64 GetApexLoginSize(_apex_send_login* apexLogin);

} // namespace Apex
} // namespace Authentication
} // namespace NexusPro
