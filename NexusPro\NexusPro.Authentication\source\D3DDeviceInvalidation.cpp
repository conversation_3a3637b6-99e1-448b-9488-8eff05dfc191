/**
 * @file D3DDeviceInvalidation.cpp
 * @brief RF Online Direct3D Device Invalidation Functions
 * @note Original Function: ?D3D_R3InvalidateDevice@@YAJXZ
 * @note Original Address: 0x14050B040
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: D3D_R3InvalidateDeviceYAJXZ_14050B040.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

// External global objects (defined elsewhere in the codebase)
extern void* stOldRenderTarget;    // Old render target surface
extern void* stOldStencilZ;        // Old stencil/depth buffer surface

// External function declarations
extern void CN_InvalidateNature();
extern void ReleaseVertexShaderList();
extern void ReleaseBlurVBuffer();
extern void ReleaseFullScreenEffect();

/**
 * @brief Invalidates the Direct3D R3 device and releases associated resources
 * @return 0 on success
 *
 * This function performs a complete invalidation of the Direct3D rendering device,
 * releasing all cached resources, shaders, buffers, and render targets. This is
 * typically called when the graphics device is lost or needs to be reset.
 */
__int64 D3D_R3InvalidateDevice() {
    // Invalidate nature rendering system (sky, sun, atmospheric effects)
    CN_InvalidateNature();
    
    // Release vertex shader resources
    // This clears all compiled vertex shaders and their associated data
    ReleaseVertexShaderList();
    
    // Release blur vertex buffer
    // This clears vertex buffers used for post-processing blur effects
    ReleaseBlurVBuffer();
    
    // Release full-screen effect resources
    // This clears resources used for full-screen post-processing effects
    ReleaseFullScreenEffect();
    
    // Release old render target if it exists
    if (stOldRenderTarget) {
        // Call the Release method through the virtual function table
        reinterpret_cast<void(*)()>(
            reinterpret_cast<void**>(stOldRenderTarget)[0]  // vfptr->Release
        )();
        
        // Clear the pointer to prevent double-release
        stOldRenderTarget = nullptr;
    }
    
    // Release old stencil/depth buffer if it exists
    if (stOldStencilZ) {
        // Call the Release method through the virtual function table
        reinterpret_cast<void(*)()>(
            reinterpret_cast<void**>(stOldStencilZ)[0]      // vfptr->Release
        )();
        
        // Clear the pointer to prevent double-release
        stOldStencilZ = nullptr;
    }
    
    // Return success
    return 0;
}

/**
 * @brief R3 device invalidation wrapper function
 * @return Result from D3D_R3InvalidateDevice (0 on success)
 *
 * This function serves as a wrapper for the D3D R3 device invalidation,
 * providing a simplified interface for R3-specific device invalidation.
 *
 * Original Function: ?R3InvalidateDevice@@YAJXZ
 * Original Address: 0x1404E9FC0
 */
__int32 R3InvalidateDevice() {
    // Call the main D3D R3 device invalidation function
    return static_cast<__int32>(D3D_R3InvalidateDevice());
}
