/**
 * @file ValidateIntegerBasedGroup.h
 * @brief RF Online Integer-Based Group Parameter Validation Declarations
 * @note Original Function: ?ValidateGroup@DL_GroupParameters_IntegerBased@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x140630680
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: ValidateGroupDL_GroupParameters_IntegerBasedCrypto_140630680.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
namespace CryptoPP {
    class DL_GroupParameters_IntegerBased;
    struct RandomNumberGenerator;
}

/**
 * @namespace NexusPro::Authentication::Cryptography
 * @brief Cryptographic validation functions for RF Online authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Validates integer-based discrete logarithm group parameters
 * @param groupParams Pointer to the integer-based group parameters
 * @param randomGen Pointer to random number generator for validation
 * @param validationLevel Validation level (0=basic, 1=extended, 2=full)
 * @return true if group parameters are valid, false otherwise
 *
 * This function performs comprehensive validation of integer-based discrete logarithm
 * group parameters including:
 * 
 * Level 0 (Basic): 
 * - Checks if prime P > 1 and is odd
 * - Checks if generator G > 1 and is odd
 * 
 * Level 1 (Extended):
 * - Validates subgroup order > 1
 * - Verifies cofactor relationships
 * - Checks group structure integrity
 * 
 * Level 2 (Full):
 * - Performs full primality testing on P and G
 * - Comprehensive cryptographic validation
 * 
 * @note Original Address: 0x140630680
 * @note Used for validating DL group parameters in RF Online's authentication system
 */
char ValidateIntegerBasedGroupParameters(
    CryptoPP::DL_GroupParameters_IntegerBased* groupParams,
    struct CryptoPP::RandomNumberGenerator* randomGen,
    unsigned int validationLevel
);

} // namespace Cryptography
} // namespace Authentication
} // namespace NexusPro
