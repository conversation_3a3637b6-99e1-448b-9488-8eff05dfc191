/*
 * NexusPro Authentication Module
 * Rendering Invalidation Header
 * 
 * Original Functions: Multiple rendering invalidation functions
 * Original Addresses: 0x1405229B0, 0x1405221E0
 * 
 * Purpose: Header for invalidation of rendering objects like sky and sun during authentication processes
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Rendering {

/**
 * @brief Invalidate sky rendering objects
 * @param this Pointer to Sky instance
 * @note Original Address: 0x1405229B0
 */
void __fastcall Sky_InvalidateSky(Sky* this);

/**
 * @brief Invalidate sun rendering objects
 * @param this Pointer to Sun instance
 * @note Original Address: 0x1405221E0
 */
void __fastcall Sun_InvalidateSun(Sun* this);

} // namespace Rendering
} // namespace Authentication
} // namespace NexusPro
