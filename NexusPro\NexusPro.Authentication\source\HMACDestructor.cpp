/*
 * NexusPro Authentication Module
 * HMAC Destructor Implementation
 * 
 * Original Function: ??1?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Original Address: 0x140464F50
 * 
 * Purpose: Destructor for CryptoPP HMAC-SHA1 message authentication code implementation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 * 
 * Changes Made:
 * - Fixed malformed template syntax
 * - Cleaned up variable naming
 * - Added proper includes and namespace
 * - Maintained original decompiled logic
 */

#include "../headers/CryptoValidation.h"
#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Crypto {

/**
 * @brief Destructor for HMAC-SHA1 Message Authentication Code Implementation
 * @param this Pointer to the object being destroyed
 * 
 * Properly cleans up the CryptoPP HMAC-SHA1 implementation by calling the base
 * algorithm destructor. This ensures proper cleanup of cryptographic resources
 * in the RF Online security system.
 */
void __fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::~MessageAuthenticationCodeImpl(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>> *this)
{
    __int64 *v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v3; // [sp+0h] [bp-28h]@1
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>> *v4; // [sp+30h] [bp+8h]@1

    v4 = this;
    v1 = &v3;
    
    // Initialize debug pattern in local variables
    for (i = 8i64; i; --i) {
        *(_DWORD *)v1 = -858993460;
        v1 = (__int64 *)((char *)v1 + 4);
    }
    
    // Call the base algorithm destructor to properly clean up resources
    CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>,CryptoPP::HMAC<CryptoPP::SHA1>>::~AlgorithmImpl(
        (CryptoPP::AlgorithmImpl<CryptoPP::SimpleKeyingInterfaceImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>,CryptoPP::HMAC<CryptoPP::SHA1>> *)&v4->vfptr);
}

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
