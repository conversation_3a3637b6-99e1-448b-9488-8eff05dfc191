/**
 * @file NatureInvalidation.cpp
 * @brief RF Online Nature System Invalidation Functions
 * @note Original Function: ?CN_InvalidateNature@@YAXXZ
 * @note Original Address: 0x140504ED0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: CN_InvalidateNatureYAXXZ_140504ED0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

// External global objects (defined elsewhere in the codebase)
extern void* unk_184A79E58;    // Sky system instance
extern DWORD dword_184A79E28;  // Sun system instance

/**
 * @brief Invalidates the nature rendering system
 *
 * This function invalidates both the sky and sun rendering systems,
 * typically called when graphics settings change or when the rendering
 * context needs to be refreshed. This ensures that nature elements
 * are properly reinitialized with updated parameters.
 */
void CN_InvalidateNature() {
    // Invalidate the sky rendering system
    // This clears cached sky textures, lighting data, and atmospheric parameters
    Sky::InvalidateSky(reinterpret_cast<Sky*>(&unk_184A79E58));
    
    // Invalidate the sun rendering system  
    // This clears cached sun position, lighting calculations, and shadow data
    Sun::InvalidateSun(reinterpret_cast<Sun*>(&dword_184A79E28));
}
