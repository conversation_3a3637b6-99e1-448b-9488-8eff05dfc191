/*
 * NexusPro Authentication Module
 * Regional Billing Login Header
 * 
 * Original Functions: Multiple regional billing login functions
 * Original Addresses: 0x14028E0F0, 0x14028E910, 0x140079030, 0x14028DBD0
 * 
 * Purpose: Header for region-specific billing login processes for different markets
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

namespace NexusPro {
namespace Authentication {
namespace Billing {

/**
 * @brief Billing ID login function
 * @param this Pointer to CBillingID instance
 * @param pUserDB Pointer to user database entry
 * @note Original Address: 0x14028E0F0
 */
void __fastcall CBillingID_Login(CBillingID* this, CUserDB* pUserDB);

/**
 * @brief Billing JP (Japan) login function
 * @param this Pointer to CBillingJP instance
 * @param pUserDB Pointer to user database entry
 * @note Original Address: 0x14028E910
 */
void __fastcall CBillingJP_Login(CBillingJP* this, CUserDB* pUserDB);

/**
 * @brief Billing Manager login function
 * @param this Pointer to CBillingManager instance
 * @param pUserDB Pointer to user database entry
 * @note Original Address: 0x140079030
 */
void __fastcall CBillingManager_Login(CBillingManager* this, CUserDB* pUserDB);

/**
 * @brief Billing NULL login function (no-op billing)
 * @param this Pointer to CBillingNULL instance
 * @param pUserDB Pointer to user database entry
 * @note Original Address: 0x14028DBD0
 */
void __fastcall CBillingNULL_Login(CBillingNULL* this, CUserDB* pUserDB);

} // namespace Billing
} // namespace Authentication
} // namespace NexusPro
