/**
 * @file ValidateECPGroup.h
 * @brief RF Online ECP Group Parameter Validation Declarations
 * @note Original Function: ?ValidateGroup@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * @note Original Address: 0x14057F300
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: ValidateGroupDL_GroupParameters_ECVECPCryptoPPCryp_14057F300.c
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

// Forward declarations
namespace CryptoPP {
    template<class EC> class DL_GroupParameters_EC;
    class ECP;
    struct RandomNumberGenerator;
}

/**
 * @namespace NexusPro::Authentication::Cryptography
 * @brief Cryptographic validation functions for RF Online authentication
 */
namespace NexusPro {
namespace Authentication {
namespace Cryptography {

/**
 * @brief Validates ECP (Elliptic Curve Prime) group parameters
 * @param groupParams Pointer to the ECP group parameters
 * @param randomGen Pointer to random number generator for validation
 * @param validationLevel Validation level (0=basic, 1=extended, 2=full)
 * @return true if group parameters are valid, false otherwise
 *
 * This function performs comprehensive validation of ECP elliptic curve group
 * parameters including:
 * 
 * Level 0 (Basic): 
 * - Basic curve parameter validation
 * - Field size consistency checks
 * 
 * Level 1 (Extended):
 * - All basic checks
 * - Additional curve structure validation
 * 
 * Level 2 (Full):
 * - Hasse bound verification (order > 4 * sqrt(field_size))
 * - Full primality testing on group order
 * - Cofactor validation and calculation
 * - Advanced cryptographic security checks
 * 
 * The validation ensures that the elliptic curve parameters meet cryptographic
 * security standards and are suitable for use in discrete logarithm cryptography.
 * 
 * @note Original Address: 0x14057F300
 * @note Used for validating ECP group parameters in RF Online's authentication system
 */
char ValidateECPGroupParameters(
    CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>* groupParams,
    struct CryptoPP::RandomNumberGenerator* randomGen,
    unsigned int validationLevel
);

} // namespace Cryptography
} // namespace Authentication
} // namespace NexusPro
