/**
 * @file BillingBaseLogin.cpp
 * @brief RF Online Base Billing Login Function
 * @note Original Function: ?Login@CBilling@@UEAAXPEAVCUserDB@@@Z
 * @note Original Address: 0x14028CAC0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>
#include <winsock2.h>

/**
 * @brief Base billing system login function
 * @param billing Pointer to the billing instance
 * @param userDatabase Pointer to user database containing login credentials
 *
 * This function handles the base billing login process, initializing memory
 * buffers and calling the virtual login function through the billing vtable.
 */
void CBilling::Login(CBilling* billing, CUserDB* userDatabase) {
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;        // Original: v2 (rdi register)
    signed __int64 loopCounter;    // Original: i (rcx register)
    char* ipAddressString;         // Original: v4 (rax register)
    __int64 stackBuffer;           // Original: v5 ([sp+0h] [bp-68h])
    __int16 billingType;           // Original: v6 ([sp+20h] [bp-48h])
    _SYSTEMTIME* endDatePtr;       // Original: v7 ([sp+28h] [bp-40h])
    int remainingTime;             // Original: v8 ([sp+30h] [bp-38h])
    _SYSTEMTIME* endDateRef;       // Original: v9 ([sp+40h] [bp-28h])
    char* cmsString;               // Original: v10 ([sp+48h] [bp-20h])
    CBillingVtbl* vtablePtr;       // Original: v11 ([sp+50h] [bp-18h])
    CBilling* currentBilling;      // Original: v12 ([sp+70h] [bp+8h])
    CUserDB* currentUserDB;        // Original: v13 ([sp+78h] [bp+10h])

    // Initialize local references
    currentUserDB = userDatabase;
    currentBilling = billing;

    // Set buffer pointer to stack buffer
    bufferPointer = &stackBuffer;

    // Initialize memory buffer with debug pattern (0xCCCCCCCC = -858993460)
    for (loopCounter = 24; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position (4 bytes)
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }

    // Extract billing information from user database
    endDateRef = &userDatabase->m_BillingInfo.stEndDate;
    cmsString = userDatabase->m_BillingInfo.szCMS;
    
    // Convert IP address to string format
    ipAddressString = inet_ntoa(
        reinterpret_cast<struct in_addr>(userDatabase->m_dwIP)
    );
    
    // Get virtual function table pointer
    vtablePtr = currentBilling->vfptr;
    
    // Extract additional billing information
    remainingTime = currentUserDB->m_BillingInfo.lRemainTime;
    endDatePtr = endDateRef;
    billingType = currentUserDB->m_BillingInfo.iType;

    // Call virtual SendMsg_Login function through vtable
    if (static_cast<unsigned char>(
        reinterpret_cast<int(__fastcall*)(CBilling*, signed __int64, char*, char*)>(
            vtablePtr->SendMsg_Login
        )(
            currentBilling,
            reinterpret_cast<signed __int64>(currentUserDB->m_szAccountID),
            ipAddressString,
            cmsString
        )
    )) {
        // If login message sent successfully, set billing no logout flag
        CUserDB::SetBillingNoLogout(currentUserDB, 0);
    }
}
