/*
 * NexusPro Authentication Module
 * HMAC Destructor Header
 * 
 * Original Function: ??1?$MessageAuthenticationCodeImpl@VHMAC_Base@CryptoPP@@V?$HMAC@VSHA1@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Original Address: 0x140464F50
 * 
 * Purpose: Header for CryptoPP HMAC-SHA1 message authentication code destructor
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace Crypto {

// Forward declarations for CryptoPP types
namespace CryptoPP {
    class HMAC_Base;
    template<class T> class HMAC;
    class SHA1;
    
    struct ClonableVtbl;
    struct SimpleKeyingInterfaceVtbl;
    
    template<class BASE, class ALGORITHM>
    class SimpleKeyingInterfaceImpl;
    
    template<class INTERFACE, class ALGORITHM>
    class AlgorithmImpl;
    
    template<class BASE, class ALGORITHM>
    class MessageAuthenticationCodeImpl {
    public:
        union {
            ClonableVtbl *vfptr;
            SimpleKeyingInterfaceVtbl *vfptr_keying;
        };
        
        // Destructor
        void __fastcall ~MessageAuthenticationCodeImpl(
            MessageAuthenticationCodeImpl<BASE, ALGORITHM> *this);
    };
}

/**
 * @brief Destructor for HMAC-SHA1 Message Authentication Code Implementation
 * @param this Pointer to the object being destroyed
 * 
 * Properly cleans up the CryptoPP HMAC-SHA1 implementation by calling the base
 * algorithm destructor. This ensures proper cleanup of cryptographic resources
 * in the RF Online security system.
 */
void __fastcall CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>>::~MessageAuthenticationCodeImpl(
    CryptoPP::MessageAuthenticationCodeImpl<CryptoPP::HMAC_Base,CryptoPP::HMAC<CryptoPP::SHA1>> *this);

} // namespace Crypto
} // namespace Authentication
} // namespace NexusPro
