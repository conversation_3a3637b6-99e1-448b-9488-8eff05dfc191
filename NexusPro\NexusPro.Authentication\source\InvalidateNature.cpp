/**
 * @file InvalidateNature.cpp
 * @brief RF Online Nature Environment Invalidation Function
 * @note Original Function: ?CN_InvalidateNature@@YAXXZ
 * @note Original Address: 0x140504ED0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: CN_InvalidateNatureYAXXZ_140504ED0.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External global objects (defined elsewhere in the codebase)
extern char unk_184A79E58[];   // Sky object data
extern DWORD dword_184A79E28;  // Sun object data

// External function declarations (defined in graphics engine)
extern "C" void InvalidateSky(void* skyObject);
extern "C" void InvalidateSun(void* sunObject);

/**
 * @brief Invalidates nature environment components (sky and sun)
 * 
 * This function invalidates the nature environment rendering components,
 * specifically the sky and sun systems. This is typically called when:
 * - Changing maps or zones
 * - Updating weather conditions
 * - Resetting environmental graphics
 * - Cleaning up rendering resources
 * 
 * The function ensures that both sky and sun rendering systems are properly
 * invalidated and will be refreshed on the next render cycle.
 */
void CN_InvalidateNature(void) {
    // Invalidate the sky rendering system
    // This clears sky textures, atmospheric effects, and related rendering data
    // Note: Sky and Sun are external classes defined in the graphics engine
    InvalidateSky(reinterpret_cast<void*>(&unk_184A79E58));

    // Invalidate the sun rendering system
    // This clears sun position, lighting calculations, and solar effects
    InvalidateSun(reinterpret_cast<void*>(&dword_184A79E28));
}
