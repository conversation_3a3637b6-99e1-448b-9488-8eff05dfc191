/**
 * @file FontDeviceInvalidation.cpp
 * @brief RF Online Font Device Invalidation Functions
 * @note Original Function: ?InvalidateDeviceObjects@CR3Font@@QEAAJXZ
 * @note Original Address: 0x140528820
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: InvalidateDeviceObjectsCR3FontQEAAJXZ_140528820.c
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdint>

/**
 * @brief Invalidates device objects for R3 font rendering
 * @param r3Font Pointer to the R3 font instance
 * @return 0 on success
 *
 * This function invalidates and releases device-dependent objects associated
 * with R3 font rendering. It's called when the graphics device is lost or
 * needs to be reset, ensuring proper cleanup of font-related resources.
 */
__int64 CR3Font::InvalidateDeviceObjects(CR3Font* r3Font) {
    // Local variables with meaningful names (original decompiled names in comments)
    CR3Font* currentFont;                      // Original: v1 (rbx register)
    __int64 devicePointer;                     // Original: v2 (rcx register)

    // Initialize current font pointer
    currentFont = r3Font;
    
    // Get the device pointer from the font object
    devicePointer = *reinterpret_cast<__int64*>(r3Font);
    
    if (devicePointer) {
        // Check if texture 1 is valid and release it
        if (reinterpret_cast<DWORD*>(currentFont)[25]) {
            // Call Release method through virtual function table
            // Offset 448 (0x1C0) corresponds to the Release method
            reinterpret_cast<void(*)()>(
                *reinterpret_cast<__int64*>(devicePointer + 448)
            )();
        }
        
        // Check if texture 2 is valid and release it
        if (reinterpret_cast<DWORD*>(currentFont)[26]) {
            // Call Release method through virtual function table
            // Using the device pointer from the font object
            reinterpret_cast<void(*)()>(
                *reinterpret_cast<__int64*>(
                    *reinterpret_cast<__int64*>(currentFont) + 448
                )
            )();
        }
    }
    
    // Clear the texture flags to indicate they are no longer valid
    reinterpret_cast<DWORD*>(currentFont)[25] = 0;  // Clear texture 1 flag
    reinterpret_cast<DWORD*>(currentFont)[26] = 0;  // Clear texture 2 flag
    
    // Perform private release of font resources
    CR3Font::PrivateRelease(currentFont);
    
    // Return success
    return 0;
}
