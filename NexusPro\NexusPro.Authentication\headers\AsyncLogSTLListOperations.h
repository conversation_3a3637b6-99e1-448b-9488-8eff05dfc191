/*
 * NexusPro Authentication Module
 * AsyncLog STL List Operations Header
 * 
 * Original Functions: Multiple STL list operations for AsyncLogInfo
 * Original Addresses: 0x1403C3760 - 0x1403C80C0
 * 
 * Purpose: Header for STL list container operations for AsyncLogInfo storage and manipulation
 * 
 * Refactored for NexusPro by: Authentication Team
 * Date: 2025-01-14
 */

#pragma once

#include <cstdint>

namespace NexusPro {
namespace Authentication {
namespace STL {

// Forward declarations for STL types
namespace std {
    template<class T> class allocator;
    template<class T1, class T2> struct pair;
    
    template<class T, class Alloc>
    class list {
    public:
        template<int N> class _Iterator;
        struct _Node;
        typedef _Node* _Nodeptr;
        
        _Nodeptr _Myhead;
        size_t _Mysize;
        
        // Member functions
        _Iterator<0>* insert(list* this_list, _Iterator<0>* _Where, pair<int const,CAsyncLogInfo *>* _Val, _Iterator<0>* result);
        _Iterator<0>* erase(list* this_list, _Iterator<0>* _Where, _Iterator<0>* result);
        void clear(list* this_list);
        _Iterator<0>* _Insert(list* this_list, _Iterator<0>* _Where, pair<int const,CAsyncLogInfo *>* _Val, _Iterator<0>* result);
        
        // Helper functions
        _Nodeptr _Buynode(list* this_list, _Nodeptr _Next, _Nodeptr _Prev);
        void _Freenode(list* this_list, _Nodeptr _Ptr);
        
        // Node structure
        struct _Node {
            _Nodeptr _Next;
            _Nodeptr _Prev;
            pair<int const,CAsyncLogInfo *> _Myval;
        };
    };
}

// Forward declaration for CAsyncLogInfo
class CAsyncLogInfo;

/**
 * @brief STL List container function declarations for AsyncLogInfo
 * 
 * These functions provide standard STL list container operations for
 * storing and manipulating CAsyncLogInfo objects as key-value pairs.
 */

// List container type alias for readability
typedef std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>> AsyncLogList;
typedef std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0> AsyncLogListIterator;

/**
 * @brief Inserts element into list
 */
AsyncLogListIterator *__fastcall AsyncLogList::insert(
    AsyncLogList *this,
    AsyncLogListIterator *_Where,
    std::pair<int const,CAsyncLogInfo *> *_Val,
    AsyncLogListIterator *result);

/**
 * @brief Erases element from list
 */
AsyncLogListIterator *__fastcall AsyncLogList::erase(
    AsyncLogList *this,
    AsyncLogListIterator *_Where,
    AsyncLogListIterator *result);

/**
 * @brief Clears all elements from list
 */
void __fastcall AsyncLogList::clear(
    AsyncLogList *this);

/**
 * @brief Internal insert helper function
 */
AsyncLogListIterator *__fastcall AsyncLogList::_Insert(
    AsyncLogList *this,
    AsyncLogListIterator *_Where,
    std::pair<int const,CAsyncLogInfo *> *_Val,
    AsyncLogListIterator *result);

} // namespace STL
} // namespace Authentication
} // namespace NexusPro
