#include "../headers/HackShieldAuth.h"

// External function declarations (from original decompiled code)
extern unsigned int _AntiCpSvr_MakeGuidReqMsg(char* buffer, const char* guidInfo);
extern void* unk_1414F2088; // Network process object
extern unsigned __int64 _security_cookie; // Security cookie for stack protection

namespace RFOnline {
namespace Authentication {
namespace Security {

bool HackShieldAuth::ProcessServerChecksumRequest(
    HACKSHEILD_PARAM_ANTICP* hackShield,
    int clientIndex)
{
    if (!hackShield) {
        return false;
    }

    // Initialize debug buffer with pattern
    __int64 debugBuffer[44];
    for (int i = 0; i < 44; ++i) {
        debugBuffer[i] = 0xCCCCCCCCCCCCCCCC; // Debug pattern
    }

    // Stack protection
    unsigned __int64 stackCookie = (unsigned __int64)debugBuffer ^ _security_cookie;

    // Initialize HackShield parameters
    hackShield->Init();

    // Prepare message buffer
    char messageBuffer[22] = {0}; // 0x16 bytes
    unsigned short messageHeader = 0;
    
    // Generate GUID request message
    unsigned int result = _AntiCpSvr_MakeGuidReqMsg(
        (char*)&messageBuffer[2], 
        hackShield->m_byGUIDClientInfo
    );
    
    messageHeader = (unsigned short)result;

    if (result != 0) {
        // Error occurred - send error response and kick client
        char errorMessage[2] = {98, 2}; // Message type and subtype
        CNetProcess::LoadSendMsg(
            unk_1414F2088,
            clientIndex,
            errorMessage,
            (char*)&messageHeader,
            0x16
        );

        // Kick client with error code
        hackShield->Kick(2, result);
        hackShield->Init();
        return true;
    }
    else {
        // Success - set client state and send request
        hackShield->m_nSocketIndex = clientIndex;
        hackShield->m_byVerifyState = (char)HackShieldVerifyState::PENDING;

        char requestMessage[2] = {98, 2}; // Message type and subtype
        CNetProcess::LoadSendMsg(
            unk_1414F2088,
            clientIndex,
            requestMessage,
            (char*)&messageHeader,
            0x16
        );
        return true;
    }
}

void HackShieldAuth::InitializeHackShield(HACKSHEILD_PARAM_ANTICP* hackShield)
{
    if (hackShield) {
        hackShield->Init();
    }
}

unsigned int HackShieldAuth::GenerateGuidRequest(
    char* buffer,
    const char* guidInfo)
{
    if (!buffer || !guidInfo) {
        return 1; // Error code
    }

    return _AntiCpSvr_MakeGuidReqMsg(buffer, guidInfo);
}

void HackShieldAuth::SendChecksumRequest(
    int clientIndex,
    char messageType,
    const char* messageData,
    unsigned int dataSize)
{
    char message[2] = {messageType, 2};
    CNetProcess::LoadSendMsg(
        unk_1414F2088,
        clientIndex,
        message,
        messageData,
        dataSize
    );
}

void HackShieldAuth::HandleVerificationFailure(
    HACKSHEILD_PARAM_ANTICP* hackShield,
    int errorType,
    unsigned int errorCode)
{
    if (hackShield) {
        hackShield->Kick(errorType, errorCode);
        hackShield->Init();
    }
}

void HackShieldAuth::SetVerificationState(
    HACKSHEILD_PARAM_ANTICP* hackShield,
    int clientIndex,
    HackShieldVerifyState state)
{
    if (hackShield) {
        hackShield->m_nSocketIndex = clientIndex;
        hackShield->m_byVerifyState = (char)state;
    }
}

} // namespace Security
} // namespace Authentication
} // namespace RFOnline
