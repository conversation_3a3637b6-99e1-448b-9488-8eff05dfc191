/*
 * MainThread.h
 * Original Function: ?AccountServerLogin@CMainThread@@QEAAXXZ
 * Original Address: 0x1401F8140
 * 
 * Description: Main thread authentication and server connection management.
 * This class handles the primary authentication flow and server login processes.
 */

#pragma once

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace Core {

/*
 * Main Thread Class
 * Purpose: Manages main authentication thread and server connections
 * Original: CMainThread
 */
class MainThread {
public:
    /*
     * Account Server Login
     * Original: AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c
     * Purpose: Initiates login process to the account server
     * This function handles the complete account server authentication flow
     */
    void __fastcall AccountServerLogin();
    
    /*
     * Constructor
     * Purpose: Initialize main thread authentication manager
     */
    MainThread();
    
    /*
     * Destructor
     * Purpose: Clean up main thread resources
     */
    ~MainThread();

    /*
     * Get World Name
     * Purpose: Returns the current world name for authentication
     */
    const char* GetWorldName() const;
    
    /*
     * Set World Name
     * Purpose: Sets the world name for authentication
     */
    void SetWorldName(const char* world_name);

private:
    // World name for authentication (original: m_szWorldName)
    char m_szWorldName[256];
    
    // Security cookie for stack protection
    unsigned __int64 m_security_cookie;
    
    // Network process handle
    void* m_net_process;
    
    /*
     * Get IP Address
     * Purpose: Retrieves the current IP address for connection
     */
    unsigned __int32 GetIPAddress();
    
    /*
     * Load Configuration
     * Purpose: Loads world and connection configuration from INI files
     */
    bool LoadConfiguration();
    
    /*
     * Initialize Network
     * Purpose: Sets up network connections for authentication
     */
    bool InitializeNetwork();
};

/*
 * Open World Request Structure
 * Purpose: Packet structure for world connection requests
 * Original: _open_world_request_wrac
 */
struct OpenWorldRequest {
    char destination[34];      // Destination buffer (original: Dest)
    unsigned __int32 ip_addr;  // IP address (original: v7)
    char hash_verify[32];      // Hash verification data (original: Dst)
    char packet_type;          // Packet type (original: pbyType)
    char flags;                // Additional flags (original: v11)
    
    /*
     * Get Packet Size
     * Purpose: Returns the size of the open world request packet
     */
    unsigned __int16 size() const;
};

} // namespace Core
} // namespace Authentication
} // namespace RFOnline
