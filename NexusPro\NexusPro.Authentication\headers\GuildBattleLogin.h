#pragma once

// Guild Battle Login Functions Header
// Handles guild battle login and member management
// Part of the RF Online Authentication Module

#include "../../NexusPro.Core/headers/NexusProCommon.h"

namespace RFOnline {
namespace Authentication {
namespace GuildBattle {

// Forward declarations
class CNormalGuildBattleGuild;
class CNormalGuildBattleGuildMember;
class CNormalGuildBattleField;
class CNormalGuildBattleLogger;
class CPlayer;

/**
 * Guild Battle Login Management
 * Handles player login to guild battles and member management
 */
class GuildBattleLogin {
public:
    /**
     * Handle guild member login to battle
     * @param guild Pointer to guild battle object
     * @param memberIndex Member index
     * @param serialNumber Player serial number
     * @param battleNumber Guild battle number
     * @param destGuild Destination guild name
     * @param playerID Player ID
     * @param battleField Pointer to battle field
     * @param logger Pointer to battle logger
     */
    static void HandleMemberLogin(
        CNormalGuildBattleGuild* guild,
        int memberIndex,
        unsigned int serialNumber,
        char battleNumber,
        char* destGuild,
        unsigned int playerID,
        CNormalGuildBattleField* battleField,
        CNormalGuildBattleLogger* logger
    );

    /**
     * Process guild member position notifications
     * @param guild Pointer to guild battle object
     * @param player Pointer to player object
     */
    static void NotifyMemberPosition(
        CNormalGuildBattleGuild* guild,
        CPlayer* player
    );

    /**
     * Handle guild join request
     * @param guild Pointer to guild battle object
     * @param memberIndex Member index
     * @param destGuild Destination guild name
     * @param logger Pointer to battle logger
     */
    static void HandleJoinRequest(
        CNormalGuildBattleGuild* guild,
        int memberIndex,
        char* destGuild,
        CNormalGuildBattleLogger* logger
    );

    /**
     * Move guild member to battle field
     * @param guild Pointer to guild battle object
     * @param memberIndex Member index
     * @param playerID Player ID
     * @param battleField Pointer to battle field
     * @param logger Pointer to battle logger
     */
    static void MoveMemberToBattle(
        CNormalGuildBattleGuild* guild,
        int memberIndex,
        unsigned int playerID,
        CNormalGuildBattleField* battleField,
        CNormalGuildBattleLogger* logger
    );
};

} // namespace GuildBattle
} // namespace Authentication
} // namespace RFOnline
